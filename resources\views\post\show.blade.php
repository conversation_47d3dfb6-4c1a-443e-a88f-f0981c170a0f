@extends('layouts.homeLayout')

@section('title', $post->title)

@section('content')
    <div class="post-detail">
        <div class="container">
            <!-- Post Header -->
            <div class="post-header">
                <a href="{{ route('home') }}" class="back-btn">
                    <ion-icon name="arrow-back-outline"></ion-icon>
                    <span>Kembali ke Beranda</span>
                </a>

                <h1 class="headline headline-1 section-title">
                    <span class="span">{{ $post->title }}</span>
                </h1>

                <div class="post-meta">
                    <div class="profile-card">
                        @if ($post->user->profile && $post->user->profile->avatar)
                            <img src="{{ asset('storage/' . $post->user->profile->avatar) }}" width="48" height="48"
                                alt="{{ $post->user->name }}" class="profile-banner">
                        @else
                            <div class="author-initial profile-banner" aria-label="{{ $post->user->name }}">
                                {{ strtoupper(substr($post->user->name, 0, 1)) }}
                            </div>
                        @endif
                        <div>
                            <p class="card-title">{{ $post->user->name }}</p>
                            <p class="card-subtitle">{{ $post->created_at->format('d F Y') }}</p>
                        </div>
                    </div>

                    <div class="post-stats">
                        <div class="stats-item">
                            <ion-icon name="eye-outline"></ion-icon>
                            <span>{{ number_format($post->views) }} Views</span>
                        </div>
                        <div class="stats-item">
                            <ion-icon name="chatbubble-outline"></ion-icon>
                            <span>{{ $post->comments->count() }} Komentar</span>
                        </div>
                    </div>
                </div>

                <!-- Post Categories & Tags -->
                <div class="post-categories">
                    @if ($post->category)
                        <a href="#" class="category-badge">{{ $post->category->title }}</a>
                    @endif

                    <div class="post-tags">
                        @foreach ($post->tags as $tag)
                            <a href="{{ route('posts.by.tag', $tag->id) }}" class="tag-link">#{{ $tag->title }}</a>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Post Image -->
            @if ($post->image)
                <div class="post-banner">
                    <img src="{{ asset('storage/' . $post->image) }}" alt="{{ $post->title }}" class="post-banner-img">
                </div>
            @endif

            <!-- Post Content -->
            <div class="post-content">
                <div class="post-text">
                    {!! $post->content !!}
                </div>

                <!-- Post Engagement Buttons -->
                <div class="post-engagement">
                    <div class="engagement-buttons">
                        <form action="{{ route('posts.like', $post->id) }}" method="POST" style="display:inline;">
                            @csrf
                            <button type="submit"
                                class="engagement-btn like-btn{{ auth()->check() &&$post->userReaction->where('user_id', auth()->id())->where('reaction_type', 'like')->count() > 0? ' active': '' }}">
                                <ion-icon name="thumbs-up-outline"></ion-icon>
                                <span>{{ $post->likes }}</span>
                            </button>
                        </form>
                        <form action="{{ route('posts.dislike', $post->id) }}" method="POST" style="display:inline;">
                            @csrf
                            <button type="submit"
                                class="engagement-btn dislike-btn{{ auth()->check() &&$post->userReaction->where('user_id', auth()->id())->where('reaction_type', 'dislike')->count() > 0? ' active': '' }}">
                                <ion-icon name="thumbs-down-outline"></ion-icon>
                                <span>{{ $post->dislikes }}</span>
                            </button>
                        </form>
                    </div>
                    <div class="share-buttons">
                        <button type="button" class="share-btn" onclick="shareOnFacebook()">
                            <ion-icon name="logo-facebook"></ion-icon>
                        </button>
                        <button type="button" class="share-btn" onclick="shareOnTwitter()">
                            <ion-icon name="logo-twitter"></ion-icon>
                        </button>
                        <button type="button" class="share-btn" onclick="shareOnWhatsapp()">
                            <ion-icon name="logo-whatsapp"></ion-icon>
                        </button>
                        <button type="button" class="share-btn" onclick="copyLink()">
                            <ion-icon name="link-outline"></ion-icon>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Comments Section -->
            <div class="comments-section">
                <h2 class="headline headline-2 section-title comment-title">
                    <span class="span">Komentar ({{ $post->comments->count() }})</span>
                </h2>

                <!-- Comment Form -->
                <div class="comment-form-container">
                    @auth
                        <form action="{{ route('comments.store', $post->id) }}" method="POST" class="comment-form">
                            @csrf
                            <div class="form-header">
                                <div class="profile-card">
                                    @if (Auth::user()->profile && Auth::user()->profile->avatar)
                                        <img src="{{ asset('storage/' . Auth::user()->profile->avatar) }}" width="40"
                                            height="40" alt="{{ Auth::user()->name }}" class="profile-banner">
                                    @else
                                        <div class="author-initial profile-banner" aria-label="{{ Auth::user()->name }}">
                                            {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                                        </div>
                                    @endif
                                    <div>
                                        <p class="card-title">{{ Auth::user()->name }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="form-content">
                                <textarea name="content" id="comment-content" placeholder="Tulis komentar Anda di sini..." required></textarea>
                            </div>

                            <div class="form-footer">
                                <button type="submit" class="btn btn-primary">Kirim Komentar</button>
                            </div>
                        </form>
                    @else
                        <div class="login-prompt">
                            <p>Silakan <a href="{{ route('login') }}" class="login-link">masuk</a> untuk meninggalkan komentar.
                            </p>
                        </div>
                    @endauth
                </div>

                <!-- Comments List -->
                <div class="comments-list">
                    @if ($post->comments->count() > 0)
                        @foreach ($post->comments as $comment)
                            <div class="comment-card" id="comment-{{ $comment->id }}">
                                <div class="comment-header">
                                    <div class="profile-card">
                                        @if ($comment->user->profile && $comment->user->profile->avatar)
                                            <img src="{{ asset('storage/' . $comment->user->profile->avatar) }}"
                                                width="40" height="40" alt="{{ $comment->user->name }}"
                                                class="profile-banner">
                                        @else
                                            <div class="author-initial profile-banner"
                                                aria-label="{{ $comment->user->name }}">
                                                {{ strtoupper(substr($comment->user->name, 0, 1)) }}
                                            </div>
                                        @endif
                                        <div>
                                            <p class="card-title">{{ $comment->user->name }}</p>
                                            <p class="card-subtitle">{{ $comment->created_at->diffForHumans() }}</p>
                                        </div>
                                    </div>

                                    @auth
                                        @if (Auth::id() === $comment->user_id || Auth::user()->role === 'admin')
                                            <div class="comment-actions">
                                                <form action="{{ route('comments.destroy', $comment->id) }}" method="POST"
                                                    class="delete-form">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="delete-btn"
                                                        onclick="return confirm('Apakah Anda yakin ingin menghapus komentar ini?')">
                                                        <ion-icon name="trash-outline"></ion-icon>
                                                    </button>
                                                </form>
                                            </div>
                                        @endif
                                    @endauth
                                </div>

                                <div class="comment-content">
                                    <p>{{ $comment->content }}</p>
                                </div>

                                <!-- Reply Button -->
                                <div class="comment-footer">
                                    <button class="reply-btn" onclick="toggleReplyForm('{{ $comment->id }}')">
                                        <ion-icon name="return-down-forward-outline"></ion-icon>
                                        <span>Balas</span>
                                    </button>
                                </div>

                                <!-- Reply Form -->
                                <div class="reply-form-container" id="reply-form-{{ $comment->id }}"
                                    style="display: none;">
                                    @auth
                                        <form action="{{ route('replies.store', $comment->id) }}" method="POST"
                                            class="reply-form">
                                            @csrf
                                            <div class="form-content">
                                                <textarea name="content" placeholder="Tulis balasan Anda di sini..." required></textarea>
                                            </div>

                                            <div class="form-footer">
                                                <button type="button" class="btn-cancel"
                                                    onclick="toggleReplyForm('{{ $comment->id }}')">Batal</button>
                                                <button type="submit" class="btn btn-primary">Kirim Balasan</button>
                                            </div>
                                        </form>
                                    @else
                                        <div class="login-prompt">
                                            <p>Silakan <a href="{{ route('login') }}" class="login-link">masuk</a> untuk
                                                membalas komentar.</p>
                                        </div>
                                    @endauth
                                </div>

                                <!-- Replies List -->
                                @if ($comment->replies->count() > 0)
                                    <div class="replies-list">
                                        @foreach ($comment->replies as $reply)
                                            <div class="reply-card" id="reply-{{ $reply->id }}">
                                                <div class="reply-header">
                                                    <div class="profile-card">
                                                        @if ($reply->user->profile && $reply->user->profile->avatar)
                                                            <img src="{{ asset('storage/' . $reply->user->profile->avatar) }}"
                                                                width="32" height="32"
                                                                alt="{{ $reply->user->name }}" class="profile-banner">
                                                        @else
                                                            <div class="author-initial profile-banner small"
                                                                aria-label="{{ $reply->user->name }}">
                                                                {{ strtoupper(substr($reply->user->name, 0, 1)) }}
                                                            </div>
                                                        @endif
                                                        <div>
                                                            <p class="card-title">{{ $reply->user->name }}</p>
                                                            <p class="card-subtitle">
                                                                {{ $reply->created_at->diffForHumans() }}</p>
                                                        </div>
                                                    </div>

                                                    @auth
                                                        @if (Auth::id() === $reply->user_id || Auth::user()->role === 'admin')
                                                            <div class="reply-actions">
                                                                <form action="{{ route('replies.destroy', $reply->id) }}"
                                                                    method="POST" class="delete-form">
                                                                    @csrf
                                                                    @method('DELETE')
                                                                    <button type="submit" class="delete-btn"
                                                                        onclick="return confirm('Apakah Anda yakin ingin menghapus balasan ini?')">
                                                                        <ion-icon name="trash-outline"></ion-icon>
                                                                    </button>
                                                                </form>
                                                            </div>
                                                        @endif
                                                    @endauth
                                                </div>

                                                <div class="reply-content">
                                                    <p>{{ $reply->content }}</p>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    @else
                        <div class="no-comments">
                            <p>Belum ada komentar. Jadilah yang pertama memberikan komentar!</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Related Posts -->
            @if ($relatedPosts->count() > 0)
                <div class="related-posts">
                    <h2 class="headline headline-2 section-title">
                        <span class="span">Artikel Terkait</span>
                    </h2>

                    <div class="related-posts-grid">
                        @foreach ($relatedPosts as $relatedPost)
                            <div class="related-post-card">
                                @if ($relatedPost->image)
                                    <div class="card-banner">
                                        <img src="{{ asset('storage/' . $relatedPost->image) }}"
                                            alt="{{ $relatedPost->title }}" class="img-cover">
                                    </div>
                                @endif

                                <div class="card-content">
                                    <h3 class="card-title">
                                        <a
                                            href="{{ route('posts.show', $relatedPost->slug) }}">{{ $relatedPost->title }}</a>
                                    </h3>

                                    <div class="card-meta">
                                        <span class="category">{{ $relatedPost->category->title ?? 'Umum' }}</span>
                                        <span class="date">{{ $relatedPost->created_at->format('d M Y') }}</span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>

    <style>
        /* Post Detail Styles */
        .post-detail {
            padding-block: var(--section-padding);
            padding-top: 120px;
            /* Menambahkan jarak antara navbar dan konten */
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--text-wild-blue-yonder);
            font-size: var(--fontSize-6);
            margin-bottom: 30px;
            transition: var(--transition-1);
        }

        .back-btn:hover {
            color: var(--text-carolina-blue);
            transform: translateX(-5px);
        }

        .post-header {
            margin-bottom: 30px;
            background-color: var(--bg-oxford-blue-2);
            padding: 25px;
            border-radius: var(--radius-16);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            border-left: 4px solid var(--bg-carolina-blue);
        }

        .post-meta {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
            margin-block: 20px;
        }

        .post-stats {
            display: flex;
            gap: 15px;
        }

        .stats-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: var(--text-wild-blue-yonder);
            font-size: var(--fontSize-6);
        }

        .post-categories {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
            margin-block: 20px;
        }

        .category-badge {
            background-color: var(--bg-carolina-blue);
            color: var(--white);
            font-size: var(--fontSize-8);
            font-weight: var(--weight-bold);
            padding: 6px 15px;
            border-radius: var(--radius-pill);
            transition: all 0.3s ease;
        }

        .category-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 120, 255, 0.3);
        }

        .post-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .tag-link {
            color: var(--text-wild-blue-yonder);
            font-size: var(--fontSize-7);
            transition: var(--transition-1);
        }

        .tag-link:hover {
            color: var(--text-carolina-blue);
        }

        .post-banner {
            border-radius: var(--radius-16);
            overflow: hidden;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        .post-banner-img {
            width: 100%;
            height: auto;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .post-banner:hover .post-banner-img {
            transform: scale(1.03);
        }

        .post-content {
            margin-bottom: 60px;
            background-color: var(--bg-oxford-blue-2);
            padding: 30px;
            border-radius: var(--radius-16);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }

        /* Enhanced CKEditor Content Styling */
        .post-text {
            color: var(--text-alice-blue);
            font-size: var(--fontSize-4);
            line-height: 1.7;
        }

        .post-text p {
            margin-bottom: 20px;
            padding: 0 10px;
        }

        .post-text h1 {
            font-size: 2em;
            font-weight: 700;
            margin-bottom: 0.6em;
            color: var(--text-columbia-blue);
            line-height: 1.2;
        }

        .post-text h2 {
            font-size: 1.70em;
            font-weight: 700;
            margin-bottom: 0.6em;
            color: var(--text-columbia-blue);
            line-height: 1.2;
        }

        .post-text h3 {
            font-size: 1.50em;
            font-weight: 600;
            margin-bottom: 0.6em;
            color: var(--text-columbia-blue);
            line-height: 1.3;
        }

        .post-text h4 {
            font-size: 1.25em;
            font-weight: 600;
            margin-bottom: 0.6em;
            color: var(--text-columbia-blue);
            line-height: 1.3;
        }

        .post-text h5 {
            font-size: 1em;
            font-weight: 600;
            margin-bottom: 0.6em;
            color: var(--text-columbia-blue);
            line-height: 1.4;
        }

        .post-text h6 {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 0.6em;
            color: var(--text-columbia-blue);
            line-height: 1.4;
        }

        .post-text ul {
            list-style-type: disc !important;
            margin-left: 1.5em !important;
            margin-bottom: 20px !important;
            padding-left: 1em !important;
            display: block !important;
        }

        .post-text ol {
            list-style-type: decimal !important;
            margin-left: 1.5em !important;
            margin-bottom: 20px !important;
            padding-left: 1em !important;
            display: block !important;
        }

        .post-text li {
            margin-bottom: 0.5em !important;
            display: list-item !important;
            list-style: inherit !important;
        }

        .post-text ul li {
            list-style-type: disc !important;
        }

        .post-text ol li {
            list-style-type: decimal !important;
        }

        .post-text a {
            color: var(--text-carolina-blue);
            text-decoration: underline;
            transition: color 0.3s ease;
        }

        .post-text a:hover {
            color: var(--bg-carolina-blue);
            text-decoration: underline;
        }

        .post-text blockquote {
            border-left: 4px solid var(--bg-carolina-blue);
            padding: 0.5em 1em;
            margin: 1.5em 0;
            background-color: rgba(0, 120, 255, 0.05);
            font-style: italic;
            color: var(--text-wild-blue-yonder);
        }

        .post-text pre {
            background-color: var(--bg-prussian-blue);
            padding: 1em;
            border-radius: 5px;
            overflow-x: auto;
            margin-bottom: 20px;
            font-family: monospace;
        }

        .post-text code {
            font-family: monospace;
            background-color: var(--bg-prussian-blue);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 0.9em;
        }

        .post-text table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            overflow-x: auto;
            display: block;
        }

        .post-text table th,
        .post-text table td {
            border: 1px solid var(--border-prussian-blue);
            padding: 8px 12px;
            text-align: left;
        }

        .post-text table th {
            background-color: var(--bg-oxford-blue-2);
            color: var(--text-columbia-blue);
            font-weight: 600;
        }

        .post-text table tr:nth-child(even) {
            background-color: rgba(0, 0, 0, 0.1);
        }

        .post-text img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 1em 0;
        }

        .post-text hr {
            border: 0;
            height: 1px;
            background-color: var(--border-prussian-blue);
            margin: 2em 0;
        }

        .post-text figure {
            margin: 1.5em 0;
            text-align: center;
        }

        .post-text figcaption {
            font-size: 0.9em;
            color: var(--text-wild-blue-yonder);
            margin-top: 0.5em;
            font-style: italic;
        }

        .post-text h1,
        .post-text h2,
        .post-text h3,
        .post-text h4,
        .post-text h5,
        .post-text h6 {
            color: var(--text-columbia-blue);
            margin-block: 30px 15px;
        }

        .post-text ul,
        .post-text ol {
            padding-left: 20px;
            margin-bottom: 20px;
        }

        .post-text li {
            margin-bottom: 10px;
        }

        .post-engagement {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid var(--border-prussian-blue);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .engagement-buttons {
            display: flex;
            gap: 15px;
        }

        .engagement-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .engagement-btn.active {
            background-color: var(--bg-carolina-blue);
            color: white;
        }

        .engagement-btn:hover {
            background-color: var(--bg-carolina-blue-hover);
        }

        .inline {
            display: inline;
        }

        .share-buttons {
            display: flex;
            gap: 10px;
        }

        .share-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--bg-prussian-blue);
            color: var(--text-wild-blue-yonder);
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .share-btn:hover {
            transform: translateY(-3px);
        }

        .share-btn:nth-child(1):hover {
            background-color: #3b5998;
            color: white;
        }

        .share-btn:nth-child(2):hover {
            background-color: #1DA1F2;
            color: white;
        }

        .share-btn:nth-child(3):hover {
            background-color: #25D366;
            color: white;
        }

        .share-btn:nth-child(4):hover {
            background-color: #6c5ce7;
            color: white;
        }

        .comments-section {
            margin-bottom: 60px;
        }

        .comment-title {
            font-size: var(--fontSize-3);
        }

        .comment-form-container {
            background-color: var(--bg-oxford-blue-2);
            border: 1px solid var(--border-prussian-blue);
            border-radius: var(--radius-16);
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .comment-form,
        .reply-form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-content textarea {
            width: 100%;
            min-height: 100px;
            background-color: var(--bg-prussian-blue);
            color: var(--text-alice-blue);
            border: 1px solid var(--border-prussian-blue);
            border-radius: var(--radius-8);
            padding: 12px;
            resize: vertical;
        }

        .form-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .login-prompt {
            text-align: center;
            padding: 20px;
            color: var(--text-wild-blue-yonder);
        }

        .login-link {
            color: var(--text-carolina-blue);
            text-decoration: underline;
        }

        .comments-list {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .comment-card,
        .reply-card {
            background-color: var(--bg-oxford-blue-2);
            border: 1px solid var(--border-prussian-blue);
            border-radius: var(--radius-16);
            padding: 20px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .comment-header,
        .reply-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .comment-content,
        .reply-content {
            color: var(--text-alice-blue);
            margin-bottom: 15px;
            font-size: var(--fontSize-6);
            line-height: 1.6;
        }

        .comment-footer {
            display: flex;
            justify-content: flex-end;
        }

        .reply-btn,
        .btn-cancel {
            background: none;
            border: none;
            color: var(--text-wild-blue-yonder);
            font-size: var(--fontSize-6);
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            transition: var(--transition-1);
        }

        .reply-btn:hover,
        .btn-cancel:hover {
            color: var(--text-carolina-blue);
        }

        .delete-btn {
            background: none;
            border: none;
            color: var(--text-wild-blue-yonder);
            cursor: pointer;
            transition: var(--transition-1);
        }

        .delete-btn:hover {
            color: #ef4444;
        }

        .replies-list {
            margin-top: 20px;
            margin-left: 30px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .reply-card {
            border-left: 2px solid var(--bg-carolina-blue);
        }

        .profile-card {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .profile-banner {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
            overflow: hidden;
        }

        .author-initial {
            width: 48px;
            height: 48px;
            background-color: var(--bg-prussian-blue);
            color: var(--text-alice-blue);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: var(--weight-bold);
            border-radius: 50%;
        }

        .author-initial.small {
            width: 32px;
            height: 32px;
            font-size: var(--fontSize-7);
        }

        .no-comments {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-wild-blue-yonder);
            font-style: italic;
            background-color: var(--bg-oxford-blue-2);
            border-radius: var(--radius-16);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .related-posts {
            margin-top: 60px;
        }

        .related-posts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .related-post-card {
            background-color: var(--bg-oxford-blue-2);
            border: 1px solid var(--border-prussian-blue);
            border-radius: var(--radius-16);
            overflow: hidden;
            transition: transform 0.3s ease;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .related-post-card:hover {
            transform: translateY(-5px);
        }

        .related-post-card .card-banner {
            height: 200px;
            overflow: hidden;
        }

        .related-post-card .card-content {
            padding: 20px;
        }

        .related-post-card .card-title {
            font-size: var(--fontSize-5);
            color: var(--text-alice-blue);
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .related-post-card .card-meta {
            display: flex;
            justify-content: space-between;
            color: var(--text-wild-blue-yonder);
            font-size: var(--fontSize-7);
        }

        @media (max-width: 768px) {
            .post-detail {
                padding-top: 80px;
            }

            .post-meta {
                flex-direction: column;
                align-items: flex-start;
            }

            .related-posts-grid {
                grid-template-columns: 1fr;
            }

            .post-header,
            .post-content {
                padding: 15px;
            }
        }
    </style>

    <script>
        function toggleReplyForm(commentId) {
            const replyForm = document.getElementById(`reply-form-${commentId}`);
            if (replyForm.style.display === 'none' || replyForm.style.display === '') {
                replyForm.style.display = 'block';
            } else {
                replyForm.style.display = 'none';
            }
        }
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadUserReaction({{ $post->id }});
        });

        function toggleReplyForm(commentId) {
            const replyForm = document.getElementById(`reply-form-${commentId}`);
            if (replyForm.style.display === 'none' || replyForm.style.display === '') {
                replyForm.style.display = 'block';
            } else {
                replyForm.style.display = 'none';
            }
        }

        function loadUserReaction(postId) {
            fetch(`/posts/${postId}/reaction`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('likeCount').textContent = data.likes;
                    document.getElementById('dislikeCount').textContent = data.dislikes;

                    const likeButton = document.getElementById('likeButton');
                    const dislikeButton = document.getElementById('dislikeButton');

                    if (data.reaction === 'like') {
                        likeButton.classList.add('active');
                    } else if (data.reaction === 'dislike') {
                        dislikeButton.classList.add('active');
                    }
                })
                .catch(error => console.error('Error loading reaction:', error));
        }

        function handleLike(postId) {
            @auth
            fetch(`/posts/${postId}/like`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    if (response.status === 401) {
                        window.location.href = "{{ route('login') }}";
                        return;
                    }
                    return response.json();
                })
                .then(data => {
                    if (data) {
                        document.getElementById('likeCount').textContent = data.likes;
                        document.getElementById('dislikeCount').textContent = data.dislikes;

                        const likeButton = document.getElementById('likeButton');
                        const dislikeButton = document.getElementById('dislikeButton');

                        if (data.status === 'liked') {
                            likeButton.classList.add('active');
                            dislikeButton.classList.remove('active');
                        } else if (data.status === 'unliked') {
                            likeButton.classList.remove('active');
                        }
                    }
                })
                .catch(error => console.error('Error liking post:', error));
        @else
            window.location.href = "{{ route('login') }}";
        @endauth
        }

        function handleDislike(postId) {
            @auth
            fetch(`/posts/${postId}/dislike`, {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                })
                .then(response => {
                    if (response.status === 401) {
                        window.location.href = "{{ route('login') }}";
                        return;
                    }
                    return response.json();
                })
                .then(data => {
                    if (data) {
                        document.getElementById('likeCount').textContent = data.likes;
                        document.getElementById('dislikeCount').textContent = data.dislikes;

                        const likeButton = document.getElementById('likeButton');
                        const dislikeButton = document.getElementById('dislikeButton');

                        if (data.status === 'disliked') {
                            dislikeButton.classList.add('active');
                            likeButton.classList.remove('active');
                        } else if (data.status === 'undisliked') {
                            dislikeButton.classList.remove('active');
                        }
                    }
                })
                .catch(error => console.error('Error disliking post:', error));
        @else
            window.location.href = "{{ route('login') }}";
        @endauth
        }

        function shareOnFacebook() {
            const url = encodeURIComponent(window.location.href);
            const title = encodeURIComponent(document.title);
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&t=${title}`, '_blank');
        }

        function shareOnTwitter() {
            const url = encodeURIComponent(window.location.href);
            const title = encodeURIComponent(document.title);
            window.open(`https://twitter.com/intent/tweet?url=${url}&text=${title}`, '_blank');
        }

        function shareOnWhatsapp() {
            const url = encodeURIComponent(window.location.href);
            const title = encodeURIComponent(document.title);
            window.open(`https://api.whatsapp.com/send?text=${title} ${url}`, '_blank');
        }

        function copyLink() {
            navigator.clipboard.writeText(window.location.href).then(() => {
                const toast = document.createElement('div');
                toast.className = 'toast-notification';
                toast.textContent = 'Link berhasil disalin!';
                document.body.appendChild(toast);

                setTimeout(() => {
                    toast.classList.add('show');
                    setTimeout(() => {
                        toast.classList.remove('show');
                        setTimeout(() => {
                            document.body.removeChild(toast);
                        }, 300);
                    }, 2000);
                }, 100);
            });
        }
    </script>

    <style>
        .toast-notification {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%) translateY(100px);
            background-color: var(--bg-carolina-blue);
            color: white;
            padding: 12px 24px;
            border-radius: var(--radius-pill);
            z-index: 1000;
            opacity: 0;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .toast-notification.show {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }
    </style>
@endsection
