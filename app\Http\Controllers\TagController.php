<?php

namespace App\Http\Controllers;

use App\Models\Tag;
use App\Models\Post;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class TagController extends Controller
{
    public function index()
    {
        $tags = Tag::withCount('posts')->paginate(10);
        return view('app.tags.index', compact('tags'));
    }

    public function create()
    {
        return view('app.tags.create');
    }

    public function store(Request $request)
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            Tag::create([
                'title' => $request->title,
                'image' => $request->file('image') ? $request->file('image')->store('tags', 'public') : null,
                'slug' => Str::slug($request->title)
            ]);

            return redirect()->route('tags.index')->with('success', 'Tag berhasil dibuat!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Gagal membuat tag: ' . $e->getMessage());
        }
    }

    public function edit(Tag $tag)
    {
        return view('app.tags.edit', compact('tag'));
    }

    public function update(Request $request, Tag $tag)
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            $data = [
                'title' => $request->title,
                'slug' => Str::slug($request->title)
            ];

            // Jika ada file image baru yang diupload
            if ($request->hasFile('image')) {
                // Hapus image lama jika ada
                if ($tag->image) {
                    Storage::disk('public')->delete($tag->image);
                }

                // Simpan image baru
                $data['image'] = $request->file('image')->store('tags', 'public');
            }

            // Jika image dihapus
            if ($request->has('has_current_image') && $request->has_current_image == '0' && $tag->image) {
                Storage::disk('public')->delete($tag->image);
                $data['image'] = null;
            }

            $tag->update($data);

            return redirect()->route('tags.index')
                ->with('success', 'Tag berhasil diperbarui!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Gagal memperbarui tag: ' . $e->getMessage());
        }
    }

    public function destroy(Tag $tag)
    {
        try {
            // Hapus image jika ada
            if ($tag->image) {
                Storage::disk('public')->delete($tag->image);
            }

            // Lepaskan hubungan dengan posts (many-to-many)
            $tag->posts()->detach();

            $tag->delete();

            return redirect()->route('tags.index')
                ->with('success', 'Tag berhasil dihapus!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Gagal menghapus tag: ' . $e->getMessage());
        }
    }

    /**
     * Menampilkan postingan berdasarkan tag
     *
     * @param  \App\Models\Tag  $tag
     * @return \Illuminate\Http\Response
     */
    public function show($slug)
    {
        $tag = Tag::where('slug', $slug)->firstOrFail();

        $posts = Post::whereHas('tags', function ($query) use ($tag) {
            $query->where('tags.id', $tag->id);
        })
            ->with(['user', 'category', 'tags'])
            ->orderBy('created_at', 'desc')
            ->paginate(9);

        return view('tags.by_tag', [
            'tag' => $tag,
            'posts' => $posts
        ]);
    }
}
