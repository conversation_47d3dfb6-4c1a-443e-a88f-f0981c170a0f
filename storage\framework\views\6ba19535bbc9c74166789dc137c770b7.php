<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['title', 'message', 'icon' => null, 'action' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['title', 'message', 'icon' => null, 'action' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div
    class="flex flex-col items-center justify-center py-12 bg-gray-700/50 rounded-xl border border-gray-600 text-center">
    <?php if($icon): ?>
        <div class="mb-4 text-indigo-400">
            <?php echo e($icon); ?>

        </div>
    <?php else: ?>
        <svg class="h-16 w-16 text-indigo-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4">
            </path>
        </svg>
    <?php endif; ?>

    <h3 class="text-xl font-medium text-white mb-2"><?php echo e($title); ?></h3>
    <p class="text-gray-400 mb-6 max-w-md"><?php echo e($message); ?></p>

    <?php if($action): ?>
        <div>
            <?php echo e($action); ?>

        </div>
    <?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/components/layout/empty-state.blade.php ENDPATH**/ ?>