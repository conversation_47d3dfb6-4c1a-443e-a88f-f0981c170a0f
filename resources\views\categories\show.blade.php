@extends('layouts.homeLayout')

@section('title', $category->title)

@section('content')
    <!-- Banner Kategori -->
    <div class="category-banner-header">
        @if ($category->banner)
            <div class="category-banner-img" style="background-image: url('{{ asset('storage/' . $category->banner) }}');">
            </div>
        @else
            <div class="category-banner-img category-banner-gradient"></div>
        @endif
        <div class="category-banner-overlay"></div>
        <div class="category-banner-content">
            <h1 class="category-banner-title">{{ $category->title }}</h1>
            <p class="category-banner-desc">{{ $category->description }}</p>
        </div>
    </div>

    <div class="section" style="padding-top: 0;">
        <div class="container">
            <div class="category-posts-header">
                <div class="category-posts-count">
                    <span class="count-number">{{ $posts->total() }}</span>
                    <span class="count-text">Art<PERSON>l dalam kategori ini</span>
                </div>
                <div class="category-filter">
                    <select class="filter-select" onchange="window.location.href=this.value">
                        <option value="{{ route('categories.show', $category->slug) }}?sort=latest" {{ request('sort') == 'latest' ? 'selected' : '' }}>Terbaru</option>
                        <option value="{{ route('categories.show', $category->slug) }}?sort=oldest" {{ request('sort') == 'oldest' ? 'selected' : '' }}>Terlama</option>
                        <option value="{{ route('categories.show', $category->slug) }}?sort=popular" {{ request('sort') == 'popular' ? 'selected' : '' }}>Terpopuler</option>
                    </select>
                </div>
            </div>

            <div class="posts-grid">
                @forelse ($posts as $post)
                    <div class="post-card">
                        <div class="card-banner">
                            <a href="{{ route('posts.show', $post->slug) }}">
                                <img src="{{ asset('storage/' . ($post->image ?? 'default.png')) }}"
                                    loading="lazy" alt="{{ $post->title }}" class="img-cover" />
                            </a>
                            <div class="card-badge-top">
                                <a href="{{ route('categories.show', $category->slug) }}"
                                   class="card-badge">{{ $category->title }}</a>
                            </div>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">
                                <a href="{{ route('posts.show', $post->slug) }}">{{ $post->title }}</a>
                            </h3>
                            <p class="card-text">{{ Str::limit(strip_tags($post->content), 120) }}</p>

                            <div class="card-meta">
                                <div class="meta-item">
                                    <ion-icon name="calendar-outline"></ion-icon>
                                    <span>{{ $post->created_at->format('d M Y') }}</span>
                                </div>
                                <div class="meta-item">
                                    <ion-icon name="eye-outline"></ion-icon>
                                    <span>{{ number_format($post->views ?? 0) }}</span>
                                </div>
                            </div>

                            <div class="card-footer">
                                <div class="card-tags">
                                    @foreach ($post->tags as $tag)
                                        <a href="{{ route('posts.by.tag', $tag->slug) }}" class="tag-link">{{ $tag->title }}</a>
                                    @endforeach
                                </div>
                                <div class="card-actions">
                                    <div class="action-item">
                                        <ion-icon name="thumbs-up-outline"></ion-icon>
                                        <span>{{ number_format($post->likes ?? 0) }}</span>
                                    </div>
                                    <div class="action-item">
                                        <ion-icon name="chatbubble-outline"></ion-icon>
                                        <span>{{ number_format($post->comments_count ?? ($post->comments->count() ?? 0)) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="empty-posts">
                        <div class="empty-icon">
                            <ion-icon name="document-text-outline"></ion-icon>
                        </div>
                        <h3>Tidak ada postingan</h3>
                        <p>Belum ada artikel yang dipublikasikan dalam kategori ini.</p>
                    </div>
                @endforelse
            </div>

            <!-- Pagination Manual -->
            <div class="pagination">
                <div class="pagination-container">
                    @if ($posts->hasPages())
                        <div class="pagination-nav">
                            {{-- Previous Page Link --}}
                            @if ($posts->onFirstPage())
                                <span class="pagination-btn disabled">
                                    <ion-icon name="chevron-back-outline"></ion-icon>
                                </span>
                            @else
                                <a href="{{ $posts->previousPageUrl() }}" class="pagination-btn">
                                    <ion-icon name="chevron-back-outline"></ion-icon>
                                </a>
                            @endif

                            {{-- Pagination Elements --}}
                            <div class="pagination-numbers">
                                @for ($i = 1; $i <= $posts->lastPage(); $i++)
                                    @if ($i == $posts->currentPage())
                                        <span class="pagination-btn active">{{ $i }}</span>
                                    @else
                                        <a href="{{ $posts->url($i) }}" class="pagination-btn">{{ $i }}</a>
                                    @endif
                                @endfor
                            </div>

                            {{-- Next Page Link --}}
                            @if ($posts->hasMorePages())
                                <a href="{{ $posts->nextPageUrl() }}" class="pagination-btn">
                                    <ion-icon name="chevron-forward-outline"></ion-icon>
                                </a>
                            @else
                                <span class="pagination-btn disabled">
                                    <ion-icon name="chevron-forward-outline"></ion-icon>
                                </span>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Banner Styles */
        .category-banner-header {
            position: relative;
            width: 100%;
            min-height: 280px;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            margin-bottom: 40px;
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .category-banner-img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            transition: transform 0.8s;
            z-index: 1;
        }

        .category-banner-header:hover .category-banner-img {
            transform: scale(1.05);
        }

        .category-banner-gradient {
            background: linear-gradient(120deg, #0ea5ea 0%, #0bd1d1 100%);
        }

        .category-banner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(20, 24, 40, 0.65);
            z-index: 2;
        }

        .category-banner-content {
            position: relative;
            z-index: 3;
            width: 100%;
            text-align: center;
            padding: 40px 16px 32px 16px;
        }

        .category-banner-title {
            color: #fff;
            font-size: 3.2rem; /* Ukuran diperbesar dari 2.8rem */
            font-weight: 800;
            margin-bottom: 12px;
            letter-spacing: 1px;
            text-shadow: 0 2px 16px rgba(0, 0, 0, 0.25);
        }

        .category-banner-desc {
            color: #e0e7ef;
            font-size: 2rem !important; /* Ukuran diperbesar dari 1.2rem */
            font-weight: 400;
            max-width: 700px;
            margin: 0 auto;
            text-shadow: 0 1px 8px rgba(0, 0, 0, 0.18);
        }

        /* Category Posts Header */
        .category-posts-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-prussian-blue);
        }

        .category-posts-count {
            display: flex;
            flex-direction: column;
        }

        .count-number {
            font-size: 2.4rem; /* Ukuran diperbesar dari 2rem */
            font-weight: 700;
            color: var(--text-carolina-blue);
        }

        .count-text {
            font-size: 1.5rem; /* Ukuran diperbesar dari 0.9rem */
            color: var(--text-wild-blue-yonder);
        }

        .filter-select {
            background-color: var(--bg-prussian-blue);
            color: var(--text-alice-blue);
            border: 1px solid var(--border-prussian-blue);
            padding: 10px 18px; /* Padding diperbesar */
            border-radius: 8px;
            font-size: 1.5rem; /* Ukuran diperbesar dari 0.9rem */
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-select:hover {
            border-color: var(--bg-carolina-blue);
        }

        /* Posts Grid */
        .posts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        /* Post Card */
        .post-card {
            background-color: var(--bg-oxford-blue);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
            border: 1px solid var(--border-prussian-blue);
        }

        .post-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
            border-color: var(--bg-carolina-blue);
        }

        .card-banner {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        .img-cover {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .post-card:hover .img-cover {
            transform: scale(1.08);
        }

        .card-badge-top {
            position: absolute;
            top: 15px;
            left: 15px;
            z-index: 2;
        }

        .card-badge {
            display: inline-block;
            background: var(--gradient-1);
            color: var(--text-white);
            font-size: 1.2rem; /* Ukuran diperbesar dari 0.8rem */
            font-weight: 600;
            padding: 8px 16px; /* Padding diperbesar */
            border-radius: 50px;
            box-shadow: 0 5px 15px rgba(14, 165, 233, 0.3);
            transition: all 0.3s ease;
        }

        .card-badge:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(14, 165, 233, 0.4);
        }

        .card-content {
            padding: 25px;
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .card-title {
            font-size: 2rem; /* Ukuran diperbesar dari 1.4rem */
            font-weight: 700;
            line-height: 1.4;
            margin-bottom: 15px;
        }

        .card-title a {
            color: var(--text-columbia-blue);
            transition: color 0.3s ease;
        }

        .card-title a:hover {
            color: var(--text-carolina-blue);
        }

        .card-text {
            color: var(--text-wild-blue-yonder);
            font-size: 1.5rem; /* Ukuran diperbesar dari 0.95rem */
            line-height: 1.6;
            margin-bottom: 20px;
            flex-grow: 1;
        }

        .card-meta {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            font-size: 1.2rem; /* Ukuran diperbesar dari 0.85rem */
            color: var(--text-wild-blue-yonder);
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .meta-item ion-icon {
            font-size: 1.3rem; /* Ukuran diperbesar dari 1.1rem */
            color: var(--text-carolina-blue);
        }

        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.05);
            margin-top: auto;
        }

        .card-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag-link {
            color: var(--text-carolina-blue);
            font-size: 1rem; /* Ukuran diperbesar dari 0.8rem */
            background-color: rgba(14, 165, 233, 0.1);
            padding: 6px 12px; /* Padding diperbesar */
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .tag-link:hover {
            background-color: var(--bg-carolina-blue);
            color: var(--text-white);
        }

        .card-actions {
            display: flex;
            gap: 12px;
        }

        .action-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: var(--text-wild-blue-yonder);
            font-size: 1rem; /* Ukuran diperbesar dari 0.85rem */
        }

        .action-item ion-icon {
            font-size: 1.3rem; /* Ukuran diperbesar dari 1.1rem */
        }

        /* Empty Posts */
        .empty-posts {
            grid-column: 1 / -1;
            text-align: center;
            padding: 60px 20px;
            background-color: var(--bg-oxford-blue);
            border-radius: 16px;
            border: 1px dashed var(--border-prussian-blue);
        }

        .empty-icon {
            font-size: 5rem; /* Ukuran diperbesar dari 4rem */
            color: var(--text-wild-blue-yonder);
            margin-bottom: 20px;
        }

        .empty-posts h3 {
            font-size: 1.8rem; /* Ukuran diperbesar dari 1.5rem */
            color: var(--text-columbia-blue);
            margin-bottom: 10px;
        }

        .empty-posts p {
            color: var(--text-wild-blue-yonder);
            font-size: 1.2rem; /* Ukuran font ditambahkan */
            max-width: 400px;
            margin: 0 auto;
        }

        /* Pagination Styles */
        .pagination {
            margin-top: 40px;
        }

        .pagination-container {
            display: flex;
            justify-content: center;
        }

        .pagination-nav {
            display: flex;
            align-items: center;
            gap: 10px;
            background-color: var(--bg-oxford-blue);
            padding: 12px 25px; /* Padding diperbesar */
            border-radius: 50px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .pagination-numbers {
            display: flex;
            gap: 8px; /* Gap diperbesar */
        }

        .pagination-btn {
            width: 45px; /* Ukuran diperbesar dari 40px */
            height: 45px; /* Ukuran diperbesar dari 40px */
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--bg-prussian-blue);
            color: var(--text-alice-blue);
            border-radius: 50%;
            font-size: 1.5rem; /* Ukuran font ditambahkan */
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .pagination-btn:hover {
            background-color: var(--bg-carolina-blue);
            transform: translateY(-3px);
        }

        .pagination-btn.active {
            background-color: var(--bg-carolina-blue);
            color: var(--text-white);
            box-shadow: 0 5px 15px rgba(14, 165, 233, 0.3);
        }

        .pagination-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .category-posts-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .posts-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }

            .pagination-nav {
                padding: 10px 18px; /* Padding diperbesar */
            }

            .pagination-btn {
                width: 40px; /* Ukuran diperbesar dari 35px */
                height: 40px; /* Ukuran diperbesar dari 35px */
            }
        }

        @media (min-width: 600px) {
            .category-banner-header {
                min-height: 320px;
            }

            .category-banner-title {
                font-size: 3.6rem; /* Ukuran diperbesar dari 3.2rem */
            }

            .category-banner-desc {
                font-size: 1.5rem; /* Ukuran diperbesar dari 1.3rem */
            }
        }

        @media (min-width: 900px) {
            .category-banner-header {
                min-height: 400px;
            }

            .category-banner-title {
                font-size: 4.5rem; /* Ukuran diperbesar dari 4rem */
            }
        }
    </style>
@endsection
