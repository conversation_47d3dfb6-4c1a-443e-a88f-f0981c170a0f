@props(['user', 'avatarUrl'])

<div class="relative ml-3">
    <div>
        <button type="button"
            class="relative flex rounded-full bg-gray-800 text-sm focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800 focus:outline-none"
            id="user-menu-button" aria-expanded="false" aria-haspopup="true"
            onclick="document.getElementById('profile-dropdown').classList.toggle('hidden')">
            <span class="absolute -inset-1.5"></span>
            <span class="sr-only">Open user menu</span>
            <img class="size-8 rounded-full object-cover"
                src="{{ $avatarUrl }}"
                alt="{{ $user->name }}"
                onerror="this.src='https://ui-avatars.com/api/?name={{ urlencode($user->name) }}&background=6366f1&color=fff'; this.onerror=null;"
            >
        </button>
    </div>
    <x-navbar.DropdownMenu :user="$user">
        <x-navbar.DropdownItem href="{{ route('profile.index') }}">Profile Anda</x-navbar.DropdownItem>
        <x-navbar.DropdownItem href="{{ route('logout') }}">Keluar</x-navbar.DropdownItem>
    </x-navbar.DropdownMenu>
</div>


