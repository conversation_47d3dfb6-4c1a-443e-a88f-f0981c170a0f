@props(['posts'])

<ul class="grid-list">
    @foreach ($posts as $post)
        <li>
            <div class="recent-post-card">
                <a href="{{ route('posts.show', $post['slug']) }}">
                    <figure class="card-banner img-holder" style="--width: 271; --height: 258">
                        <img src="{{ $post['img'] }}" width="271" height="258" loading="lazy" alt="{{ $post['alt'] }}"
                            class="img-cover">
                    </figure>
                </a>
                <div class="card-content">
                    <a href="{{ route('categories.show', $post['badge']) }}" class="card-badge">{{ $post['badge'] }}</a>
                    <h3 class="headline headline-3 card-title">
                        <a href="{{ route('posts.show', $post['slug']) }}" class="link hover-2">
                            {{ $post['title'] }}
                        </a>
                    </h3>
                    <p class="card-text">
                        {{ $post['desc'] }}
                    </p>
                    <div class="card-wrapper">
                        <div class="card-tag">
                            @foreach ($post['tags'] as $tag)
                                <a href="{{ route('posts.by.tag', $tag['slug']) }}" class="span hover-2">
                                    {{ $tag['title'] }}
                                </a>
                            @endforeach
                        </div>

                        <div class="wrapper">
                            <div class="reaction-stats">
                                <span class="reaction-item">
                                    <ion-icon name="thumbs-up-outline" aria-hidden="true"></ion-icon>
                                    {{ $post['likes'] }}
                                </span>
                                <span class="reaction-item">
                                    <ion-icon name="thumbs-down-outline" aria-hidden="true"></ion-icon>
                                    {{ $post['dislikes'] }}
                                </span>
                                <span class="reaction-item">
                                    <ion-icon name="chatbubble-outline" aria-hidden="true"></ion-icon>
                                    {{ $post['comments_count'] }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </li>
    @endforeach
</ul>

<style>
    .reaction-stats {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .reaction-item {
        display: flex;
        align-items: center;
        gap: 5px;
        color: var(--text-wild-blue-yonder);
        font-size: var(--fontSize-6);
    }

    .reaction-item ion-icon {
        font-size: 1.2em;
    }
</style>
