@extends('layouts.appLayout')

@section('title', 'Edit Struktur Organisasi')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-white">
            <span class="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400">
                Edit Struktur Organisasi
            </span>
        </h1>
        <p class="mt-2 text-gray-400">Edit data posisi dalam struktur organisasi</p>
    </div>

    <div class="bg-gray-800 shadow overflow-hidden sm:rounded-lg p-6">
        <form action="{{ route('organization.update', $organization) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="group">
                    <label for="position_name" class="block text-sm font-medium text-gray-300 mb-2"><PERSON><PERSON></label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <input type="text" name="position_name" id="position_name"
                            value="{{ old('position_name', $organization->position_name) }}"
                            class="pl-12 py-3 mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm text-base border-gray-700 bg-gray-700 text-white rounded-md transition duration-150 ease-in-out"
                            placeholder="Contoh: Ketua, Wakil Ketua, Sekretaris" required>
                    </div>
                    @error('position_name')
                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div class="group">
                    <label for="name" class="block text-sm font-medium text-gray-300 mb-2">Nama Pengurus</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <input type="text" name="name" id="name"
                            value="{{ old('name', $organization->name) }}"
                            class="pl-12 py-3 mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm text-base border-gray-700 bg-gray-700 text-white rounded-md transition duration-150 ease-in-out"
                            placeholder="Nama lengkap pengurus" required>
                    </div>
                    @error('name')
                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div class="group">
                    <label for="order" class="block text-sm font-medium text-gray-300 mb-2">Urutan</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                            </svg>
                        </div>
                        <input type="number" name="order" id="order"
                            value="{{ old('order', $organization->order) }}"
                            class="pl-12 py-3 mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm text-base border-gray-700 bg-gray-700 text-white rounded-md transition duration-150 ease-in-out"
                            placeholder="Urutan tampilan (angka lebih kecil tampil lebih dulu)">
                    </div>
                    @error('order')
                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div class="group">
                    <label for="image" class="block text-sm font-medium text-gray-300 mb-2">Foto</label>
                    @if ($organization->image)
                    <div class="mt-2 mb-3">
                        <div class="relative group/image">
                            <img src="{{ Storage::url($organization->image) }}" alt="{{ $organization->name }}"
                                class="h-24 w-24 object-cover rounded-lg border-2 border-indigo-500 shadow-lg">
                            <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover/image:opacity-100 flex items-center justify-center transition-opacity duration-200 rounded-lg">
                                <span class="text-white text-xs">Foto saat ini</span>
                            </div>
                        </div>
                    </div>
                    @endif
                    <div class="mt-1 flex items-center">
                        <div class="relative w-full">
                            <input type="file" name="image" id="image" accept="image/*"
                                class="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                                onchange="updateFileNameDisplay(this)">
                            <div class="px-4 py-3 bg-gray-700 border border-gray-600 rounded-md flex items-center justify-between text-base text-gray-300 w-full">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    <span id="file-name-display">Pilih file foto baru</span>
                                </div>
                                <span class="bg-indigo-600 px-3 py-1.5 rounded text-sm text-white">Browse</span>
                            </div>
                        </div>
                    </div>
                    <p class="mt-2 text-xs text-gray-400">Format: JPG, PNG, GIF. Maks: 2MB. Biarkan kosong jika tidak ingin mengubah foto.</p>
                    @error('image')
                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div class="md:col-span-2">
                    <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Deskripsi</label>
                    <div class="relative">
                        <textarea name="description" id="description" rows="4"
                            class="mt-1 py-3 px-4 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm text-base border-gray-700 bg-gray-700 text-white rounded-md transition duration-150 ease-in-out"
                            placeholder="Deskripsi singkat tentang posisi atau pengurus (opsional)">{{ old('description', $organization->description) }}</textarea>
                    </div>
                    @error('description')
                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                    @enderror
                </div>

                <div class="md:col-span-2">
                    <div class="flex items-center">
                        <input type="checkbox" name="is_active" id="is_active" value="1"
                            {{ $organization->is_active ? 'checked' : '' }}
                            class="h-5 w-5 text-indigo-600 focus:ring-indigo-500 border-gray-700 bg-gray-700 rounded">
                        <label for="is_active" class="ml-3 block text-base text-gray-300">
                            Aktif
                        </label>
                    </div>
                    <p class="mt-2 text-xs text-gray-400">Jika dicentang, posisi ini akan ditampilkan di halaman publik</p>
                </div>
            </div>

            <div class="mt-8 flex items-center justify-end space-x-4">
                <a href="{{ route('organization.index') }}"
                    class="inline-flex items-center px-5 py-2.5 border border-gray-600 rounded-md shadow-sm text-base font-medium text-gray-300 bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 focus:ring-offset-gray-800 transition duration-150 ease-in-out">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Batal
                </a>
                <button type="submit"
                    class="inline-flex items-center px-5 py-2.5 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 focus:ring-offset-gray-800 transition duration-150 ease-in-out">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Simpan Perubahan
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    function updateFileNameDisplay(input) {
        const fileNameDisplay = document.getElementById('file-name-display');
        if (input.files.length > 0) {
            fileNameDisplay.textContent = input.files[0].name;
        } else {
            fileNameDisplay.textContent = 'Pilih file foto baru';
        }
    }
</script>
@endsection
