<div class="relative">
    <button type="button"
        class="relative rounded-full bg-gray-800 p-1 text-gray-400 hover:text-white focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800 focus:outline-none"
        id="notification-button" aria-expanded="false" aria-haspopup="true"
        onclick="closeOtherDropdowns('notification-dropdown'); document.getElementById('notification-dropdown').classList.toggle('hidden')">
        <span class="absolute -inset-1.5"></span>
        <span class="sr-only">View notifications</span>
        <svg class="size-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
            aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round"
                d="M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0" />
        </svg>
        @if ($notifications->whereNull('read_at')->count() > 0)
            <span
                class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
                {{ $notifications->whereNull('read_at')->count() }}
            </span>
        @endif
    </button>

    <div id="notification-dropdown"
        class="absolute right-0 z-10 mt-2 w-64 origin-top-right rounded-md bg-indigo-900 shadow-lg ring-1 ring-indigo-700 focus:outline-none hidden"
        role="menu" aria-orientation="vertical" aria-labelledby="notification-button" tabindex="-1">
        <div class="px-4 py-2 text-sm text-indigo-100 border-b border-indigo-700">
            <div class="font-semibold">Notifikasi</div>
        </div>
        @if ($notifications->count() > 0)
            @foreach ($notifications as $notification)
                <a href="{{ $notification->data['href'] }}"
                    class="block px-4 py-2 text-sm {{ $notification->read_at ? 'text-indigo-300' : 'text-indigo-100' }} hover:bg-indigo-700"
                    role="menuitem" tabindex="-1" onclick="markAsRead('{{ $notification->id }}')">
                    <div class="flex items-center">
                        <span class="mr-2">{{ $notification->data['icon'] }}</span>
                        <div>
                            <div>{{ $notification->data['message'] }}</div>
                            <div class="text-xs {{ $notification->read_at ? 'text-indigo-400' : 'text-indigo-300' }}">
                                {{ $notification->created_at->diffForHumans() }}</div>
                        </div>
                    </div>
                </a>
            @endforeach
            <a href="{{ route('notifications.index') }}"
                class="block px-4 py-2 text-sm text-indigo-100 hover:bg-indigo-700">
                Lihat Semua Notifikasi
            </a>
        @else
            <div class="px-4 py-2 text-sm text-indigo-300">Tidak ada notifikasi baru</div>
        @endif
    </div>
</div>
