@extends('layouts.homeLayout')

@section('title', $tag->title . ' - Tag')

@section('content')
    <section class="tag-detail">
        <div class="container">
            <!-- Header Tag -->
            <div class="tag-header">
                <div class="tag-banner">
                    @if ($tag->image)
                        <div class="banner-overlay"></div>
                        <img src="{{ asset('storage/' . $tag->image) }}" alt="{{ $tag->title }}" class="banner-img">
                    @else
                        <div class="banner-gradient"></div>
                    @endif

                    <div class="banner-content">
                        <div class="banner-info">
                            <h1 class="headline-1">{{ $tag->title }}</h1>
                            <p class="post-count">
                                {{ $posts->total() }} Artikel
                            </p>
                        </div>
                        <a href="{{ route('tags.index') }}" class="btn btn-back">
                            <ion-icon name="arrow-back-outline"></ion-icon>
                            <span>Ke<PERSON><PERSON> ke Daftar Tag</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Search Section -->
            <div class="search-section">
                <div class="search-wrapper">
                    <div class="search-info">
                        <span>Menampilkan postingan dengan tag:</span>
                        <span class="tag-badge">{{ $tag->title }}</span>
                    </div>

                    <form action="{{ route('posts.by.tag', $tag->slug) }}" method="GET" class="search-form">
                        <input type="text" name="search" placeholder="Cari postingan..." class="search-input">
                        <button type="submit" class="search-btn">
                            <ion-icon name="search-outline" style="padding: 0 5px"></ion-icon>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Posts Grid -->
            <div class="posts-grid">
                @forelse ($posts as $post)
                    <article class="post-card">
                        <a href="{{ route('posts.show', $post->slug) }}" class="card-link">
                            <div class="card-media">
                                @if ($post->image)
                                    <img src="{{ asset('storage/' . $post->image) }}" alt="{{ $post->title }}"
                                        class="card-img">
                                @else
                                    <div class="card-img-placeholder">
                                        <ion-icon name="image-outline" class="placeholder-icon"></ion-icon>
                                    </div>
                                @endif
                            </div>

                            <div class="card-content">
                                <div class="author-info">
                                    @if ($post->user->profile && $post->user->profile->avatar)
                                        <img class="author-avatar"
                                            src="{{ asset('storage/' . $post->user->profile->avatar) }}"
                                            alt="{{ $post->user->name }}">
                                    @else
                                        <img class="author-avatar"
                                            src="https://ui-avatars.com/api/?name={{ urlencode($post->user->name) }}&color=7F9CF5&background=EBF4FF"
                                            alt="{{ $post->user->name }}">
                                    @endif
                                    <div class="author-meta">
                                        <p class="author-name">{{ $post->user->name }}</p>
                                        <time class="post-date">{{ $post->created_at->format('d M Y') }}</time>
                                    </div>
                                </div>

                                <h3 class="card-title">{{ $post->title }}</h3>
                                <p class="card-excerpt">{{ Str::limit($post->description, 100) }}</p>

                                <div class="card-footer">
                                    <div class="card-meta">
                                        <span class="category-badge">{{ $post->category->title }}</span>
                                    </div>
                                    <span class="read-more">Baca Selengkapnya <ion-icon name="arrow-forward-outline"></ion-icon></span>
                                </div>
                            </div>
                        </a>
                    </article>
                @empty
                    <div class="empty-state">
                        <ion-icon name="document-text-outline"></ion-icon>
                        <p>Tidak ada postingan ditemukan.</p>
                    </div>
                @endforelse
            </div>

            <!-- Pagination -->
            <div class="pagination">
                <div class="pagination-container">
                    @if ($posts->hasPages())
                        <div class="pagination-nav">
                            {{-- Previous Page Link --}}
                            @if ($posts->onFirstPage())
                                <span class="pagination-btn disabled">
                                    <ion-icon name="chevron-back-outline"></ion-icon>
                                </span>
                            @else
                                <a href="{{ $posts->previousPageUrl() }}" class="pagination-btn">
                                    <ion-icon name="chevron-back-outline"></ion-icon>
                                </a>
                            @endif

                            {{-- Pagination Elements --}}
                            <div class="pagination-numbers">
                                @for ($i = 1; $i <= $posts->lastPage(); $i++)
                                    @if ($i == $posts->currentPage())
                                        <span class="pagination-btn active">{{ $i }}</span>
                                    @else
                                        <a href="{{ $posts->url($i) }}" class="pagination-btn">{{ $i }}</a>
                                    @endif
                                @endfor
                            </div>

                            {{-- Next Page Link --}}
                            @if ($posts->hasMorePages())
                                <a href="{{ $posts->nextPageUrl() }}" class="pagination-btn">
                                    <ion-icon name="chevron-forward-outline"></ion-icon>
                                </a>
                            @else
                                <span class="pagination-btn disabled">
                                    <ion-icon name="chevron-forward-outline"></ion-icon>
                                </span>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>

    <style>
        .tag-detail {
            padding-block: var(--section-padding);
        }

        .container {
            padding-inline: 16px;
            max-width: 1200px;
            margin-inline: auto;
        }

        /* Tag Header Styles */
        .tag-header {
            margin-bottom: 32px;
        }

        .tag-banner {
            position: relative;
            border-radius: var(--radius-16);
            overflow: hidden;
            background-color: var(--bg-oxford-blue);
        }

        .banner-overlay {
            position: absolute;
            inset: 0;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
        }

        .banner-gradient {
            height: 300px;
            background: linear-gradient(135deg, var(--gradient-1));
        }

        .banner-img {
            width: 100%;
            height: 300px;
            object-fit: cover;
        }

        .banner-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 32px;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            gap: 20px;
            flex-wrap: wrap;
        }

        .headline-1 {
            color: var(--text-white);
            font-size: var(--fontSize-1);
            font-weight: var(--weight-bold);
            margin-bottom: 8px;
        }

        .post-count {
            color: var(--text-wild-blue-yonder);
            font-size: var(--fontSize-6);
        }

        .btn-back {
            display: flex;
            align-items: center;
            gap: 8px;
            background-color: var(--bg-carolina-blue);
            color: var(--text-white);
            padding: 12px 24px;
            border-radius: var(--radius-pill);
            transition: var(--transition-1);
        }

        .btn-back:hover {
            background-color: var(--bg-prussian-blue);
            transform: translateY(-2px);
        }

        .btn-icon {
            width: 20px;
            height: 20px;
        }

        /* Search Section Styles */
        .search-section {
            margin-bottom: 48px;
        }

        .search-wrapper {
            background-color: var(--bg-oxford-blue);
            padding: 24px;
            border-radius: var(--radius-16);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        @media (min-width: 768px) {
            .search-wrapper {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }
        }

        .search-info {
            color: var(--text-wild-blue-yonder);
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .tag-badge {
            background-color: var(--bg-carolina-blue);
            color: var(--text-white);
            padding: 4px 12px;
            border-radius: var(--radius-pill);
            font-size: var(--fontSize-7);
        }

        .search-form {
            display: flex;
            gap: 8px;
            width: 100%;
            max-width: 400px;
        }

        .search-input {
            width: 100%;
            background-color: var(--bg-prussian-blue);
            color: var(--text-white);
            padding: 12px 16px;
            border-radius: var(--radius-pill);
            border: 1px solid var(--bg-carolina-blue);
        }

        .search-input::placeholder {
            color: var(--text-wild-blue-yonder);
        }

        .search-btn {
            background-color: var(--bg-carolina-blue);
            color: var(--text-white);
            padding: 12px;
            border-radius: var(--radius-pill);
            transition: var(--transition-1);
        }

        .search-btn:hover {
            background-color: var(--bg-prussian-blue);
        }

        /* Posts Grid Styles */
        .posts-grid {
            display: grid;
            gap: 32px;
            grid-template-columns: 1fr;
        }

        @media (min-width: 768px) {
            .posts-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .posts-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        .post-card {
            background-color: var(--bg-oxford-blue);
            border-radius: var(--radius-16);
            overflow: hidden;
            transition: var(--transition-1);
        }

        .post-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-2);
        }

        .card-media {
            position: relative;
            aspect-ratio: 16/9;
        }

        .card-img,
        .card-img-placeholder {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .card-img-placeholder {
            background-color: var(--bg-prussian-blue);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .placeholder-icon {
            width: 48px;
            height: 48px;
            color: var(--text-wild-blue-yonder);
        }

        .card-content {
            padding: 24px;
        }

        /* Author Info Styles */
        .author-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .author-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .author-meta {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .author-name {
            color: var(--text-white);
            font-size: var(--fontSize-6);
            font-weight: var(--weight-semiBold);
        }

        .post-date {
            color: var(--text-wild-blue-yonder);
            font-size: var(--fontSize-7);
        }

        .card-title {
            color: var(--text-white);
            font-size: var(--fontSize-5);
            font-weight: var(--weight-semiBold);
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .card-excerpt {
            color: var(--text-wild-blue-yonder);
            font-size: var(--fontSize-6);
            margin-bottom: 20px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Card Footer Styles */
        .card-footer {
            margin-top: 20px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .card-meta {
            display: flex;
            flex: row;
            flex-wrap: wrap;
            gap: 8px;
        }

        .category-badge {
            background-color: var(--bg-prussian-blue);
            color: var(--text-carolina-blue);
            padding: 4px 12px;
            border-radius: var(--radius-pill);
            font-size: var(--fontSize-7);
        }

        .tags-wrapper {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag-badge {
            color: #000;
            font-size: var(--fontSize-7);
            transition: var(--transition-1);
        }

        .tag-badge:hover {
            color: var(--text-carolina-blue);
        }

        .tag-badge.active {
            color: var(--text-carolina-blue);
            font-weight: var(--weight-semiBold);
        }

        .read-more {
            display: flex;
            align-items: center;
            gap: 4px;
            color: var(--text-carolina-blue);
            font-size: var(--fontSize-6);
            font-weight: var(--weight-semiBold);
            transition: var(--transition-1);
        }

        .read-more:hover {
            gap: 8px;
        }

        .read-more ion-icon {
            font-size: 18px;
        }

        .no-posts {
            text-align: center;
            color: var(--text-wild-blue-yonder);
            padding: 48px;
        }

        /* Pagination Styles */
        .pagination {
            margin-top: 48px;
            text-align: center;
        }

        .pagination-container {
            display: inline-block;
        }

        .pagination-nav {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .pagination-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background-color: var(--bg-oxford-blue);
            color: var(--text-white);
            border: 1px solid var(--bg-carolina-blue);
            border-radius: var(--radius-pill);
            transition: var(--transition-1);
        }

        .pagination-btn:hover {
            background-color: var(--bg-prussian-blue);
        }

        .pagination-btn.disabled {
            background-color: var(--bg-prussian-blue);
            color: var(--text-wild-blue-yonder);
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background-color: var(--bg-carolina-blue);
            color: var(--text-white);
        }

        .pagination-numbers {
            display: flex;
            gap: 8px;
        }
    </style>
@endsection






