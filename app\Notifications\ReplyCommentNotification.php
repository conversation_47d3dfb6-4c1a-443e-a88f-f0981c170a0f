<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Post;

class ReplyCommentNotification extends Notification
{
    use Queueable;

    protected $postId;
    protected $postTitle;
    protected $commentId;
    protected $replyId;
    protected $replyContent;
    protected $replyAuthor;
    protected $postSlug;

    /**
     * Create a new notification instance.
     */
    public function __construct($postId, $postTitle, $commentId, $replyId, $replyContent, $replyAuthor, $postSlug = null)
    {
        $this->postId = $postId;
        $this->postTitle = $postTitle;
        $this->commentId = $commentId;
        $this->replyId = $replyId;
        $this->replyContent = $replyContent;
        $this->replyAuthor = $replyAuthor;
        $this->postSlug = $postSlug;

        // If slug is not provided, try to get it from the post
        if ($this->postSlug === null) {
            $post = Post::find($postId);
            if ($post) {
                $this->postSlug = $post->slug;
            }
        }
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->line('Ada balasan pada komentar Anda.')
            ->action('Lihat Balasan', $this->postSlug ? route('posts.show', $this->postSlug) : route('posts.show', $this->postId))
            ->line('Terima kasih telah menggunakan aplikasi kami!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'icon' => '↩️',
            'message' => "{$this->replyAuthor} membalas komentar Anda pada postingan \"{$this->postTitle}\"",
            'href' => $this->postSlug ? route('posts.show', $this->postSlug) : route('posts.show', $this->postId),
            'post_id' => $this->postId,
            'comment_id' => $this->commentId,
            'reply_id' => $this->replyId,
            'reply_content' => $this->replyContent,
            'reply_author' => $this->replyAuthor
        ];
    }
}
