@extends('layouts.authLayout')

@section('title', 'Masuk')

@section('content')
    <x-logo.LogoAuth />
    <form method="POST" action="{{ route('login') }}" id="loginForm" class="w-full">
        @csrf
        <x-auth.AuthTitle title="Masuk" desc="Silakan login untuk mengakses dashboard dan fitur lainnya." />
        <x-form.input
            name="email"
            type="email"
            label="Email"
            placeholder="Masukkan email"
            required="true"
            value="{{ old('email') }}"
        />
        <x-form.input
            name="password"
            type="password"
            label="Password"
            placeholder="Masukkan password"
            required="true"
        />
        <x-form.button type="submit" class="w-full mt-4">Login</x-form.button>
        <p class="text-center text-sm mt-4 text-gray-400">
            Belum punya akun?
            <a href="{{ route('register') }}" class="text-indigo-600 hover:underline">Mendaftar</a>
        </p>
    </form>
    <x-auth.AuthFooter />
@endsection

<script>
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();

        fetch(this.action, {
                method: 'POST',
                body: new FormData(this),
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessAlert(data.message, data.redirect);
                } else {
                    showErrorAlert(data.message);
                }
            })
            .catch(error => {
                showErrorAlert('Terjadi kesalahan. Silakan coba lagi.');
            });
    });
</script>

