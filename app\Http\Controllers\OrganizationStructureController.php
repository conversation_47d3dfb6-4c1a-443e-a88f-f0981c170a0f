<?php

namespace App\Http\Controllers;

use App\Models\OrganizationStructure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class OrganizationStructureController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'can:dashboard-admin']);
    }

    public function index()
    {
        $structures = OrganizationStructure::orderBy('order')->get();
        return view('app.organization.index', compact('structures'));
    }

    public function create()
    {
        return view('app.organization.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'position_name' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
        ]);

        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('organization', 'public');
        }

        $validated['is_active'] = $request->has('is_active');

        OrganizationStructure::create($validated);

        return redirect()->route('organization.index')
            ->with('success', 'Struktur organisasi berhasil ditambahkan');
    }

    public function show($id)
    {
        $member = OrganizationStructure::where('id', $id)->firstOrFail();
        $relatedPosts = [];
        if (method_exists($member, 'posts')) {
            $relatedPosts = $member->posts()->latest()->take(3)->get();
        }
        $departmentMembers = OrganizationStructure::where('position_name', $member->position_name)
            ->where('id', '!=', $member->id)
            ->get();

        return view('structure.show', compact('member', 'relatedPosts', 'departmentMembers'));
    }

    public function edit(OrganizationStructure $organization)
    {
        return view('app.organization.edit', compact('organization'));
    }

    public function update(Request $request, OrganizationStructure $organization)
    {
        $validated = $request->validate([
            'position_name' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'order' => 'nullable|integer',
            'is_active' => 'nullable|boolean',
        ]);

        if ($request->hasFile('image')) {
            // Hapus gambar lama jika ada
            if ($organization->image) {
                Storage::disk('public')->delete($organization->image);
            }
            $validated['image'] = $request->file('image')->store('organization', 'public');
        }

        $validated['is_active'] = $request->has('is_active');

        $organization->update($validated);

        return redirect()->route('organization.index')
            ->with('success', 'Struktur organisasi berhasil diperbarui');
    }

    public function destroy(OrganizationStructure $organization)
    {
        // Hapus gambar jika ada
        if ($organization->image) {
            Storage::disk('public')->delete($organization->image);
        }

        $organization->delete();

        return redirect()->route('organization.index')
            ->with('success', 'Struktur organisasi berhasil dihapus');
    }

    public function frontPage()
    {
        $structures = OrganizationStructure::where('is_active', true)
            ->orderBy('order')
            ->get();

        return view('organization.index', compact('structures'));
    }

}
