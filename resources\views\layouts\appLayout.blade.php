<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Auth') - {{ config('app.name') }}</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="shortcut icon" href="{{ asset('home/favicon_IMM_Al_Qossam.png') }}" type="image/x-icon">
    @stack('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="{{ asset('js/sweet-alert.js') }}"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        .profile-banner {
            border-radius: 50% !important;
            object-fit: cover !important;
            aspect-ratio: 1/1 !important;
            overflow: hidden !important;
        }
    </style>
</head>

<body class="bg-[hsla(222,44%,13%,1)]">
    <x-navbar.NavbarApp />
    <div class="absolute left-0 top-0 -z-10">
        <img src="{{ asset('home/assets/images/shadow-1.svg') }}" width="500" height="800" alt="Shadow 1"
            class="opacity-60 blur-sm" />
    </div>
    <div class="absolute right-0 bottom-0 -z-10">
        <img src="{{ asset('home/assets/images/shadow-2.svg') }}" width="500" height="500" alt="Shadow 2"
            class="opacity-60 blur-sm" />
    </div>
    <main class="container mx-auto px-4 py-8">
        @yield('content')
    </main>
    <footer class="my-12 text-center">
        <p class="text-gray-400 text-sm">
            IMM Al-Qossam Dashboard - Versi 1.0 -
            <a href="https://wa.me/082338520959" target="_blank"
                class="text-indigo-400 hover:text-indigo-300 cursor-pointer">
                Laporkan masalah
            </a>
        </p>
    </footer>
    @stack('scripts')

    <!-- Flash Messages -->
    <script>
        @if (session('success'))
            showSuccessAlert(
                "{{ session('success') }}",
                "{{ session('redirect') ?? '' }}"
            );
        @endif

        @if (session('error'))
            showErrorAlert("{{ session('error') }}");
        @endif

        @if ($errors->any())
            showErrorAlert("{{ $errors->first() }}");
        @endif
    </script>
</body>

</html>
