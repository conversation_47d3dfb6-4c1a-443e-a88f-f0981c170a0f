@extends('layouts.authLayout')

@section('title', 'Daftar')

@section('content')
    <x-logo.LogoAuth />
    <form method="POST" action="{{ route('register') }}" id="registerForm" class="w-full">
        @csrf
        <x-auth.AuthTitle title="Daftar" desc="Buat akun baru untuk mengakses fitur lengkap." />
        <x-form.input
            name="name"
            type="text"
            label="Nama"
            placeholder="Masukkan nama lengkap"
            required="true"
            value="{{ old('name') }}"
        />
        <x-form.input
            name="email"
            type="email"
            label="Email"
            placeholder="Masukkan email"
            required="true"
            value="{{ old('email') }}"
        />
        <x-form.input
            name="password"
            type="password"
            label="Password"
            placeholder="Masukkan password"
            required="true"
        />
        <x-form.input
            name="password_confirmation"
            type="password"
            label="Konfirmasi Password"
            placeholder="Konfirmasi password"
            required="true"
        />
        <x-form.button type="submit" class="w-full mt-4">Daftar</x-form.button>
        <p class="text-center text-sm mt-4 text-gray-400">
            Sudah punya akun?
            <a href="{{ route('login') }}" class="text-indigo-600 hover:underline">Masuk</a>
        </p>
    </form>
    <x-auth.AuthFooter />
@endsection

<script>
document.getElementById('registerForm').addEventListener('submit', function(e) {
    e.preventDefault();

    fetch(this.action, {
        method: 'POST',
        body: new FormData(this),
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessAlert(data.message, data.redirect);
        } else {
            showErrorAlert(data.message);
        }
    })
    .catch(error => {
        showErrorAlert('Terjadi kesalahan. Silakan coba lagi.');
    });
});
</script>



