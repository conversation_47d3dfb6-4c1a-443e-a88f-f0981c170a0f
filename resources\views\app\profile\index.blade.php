@extends('layouts.appLayout')

@section('title', 'Profile')

@section('content')
    <div class="max-w-4xl mx-auto py-6 sm:py-10 px-4 sm:px-6 lg:px-8">
        <div class="bg-gray-800 shadow-xl rounded-lg overflow-hidden">
            <!-- Profile Header -->
            <x-profile.header :name="$user->name" :email="$user->email">
                <x-slot:slot>
                    <x-profile.avatar-enhanced :user="$user" :profile="$profile" />
                </x-slot:slot>

                <x-slot:actions>
                    <button
                        class="text-sm px-3 py-1 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors duration-200 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        Pengaturan
                    </button>
                </x-slot:actions>
            </x-profile.header>

            <!-- Profile Content -->
            <form id="profile-form" class="p-6" action="{{ route('profile.update') }}" method="POST">
                @csrf
                @method('PUT')

                <!-- Errors display -->
                <div id="validation-errors" class="mb-4 hidden">
                    <div class="bg-red-900/60 text-red-200 p-4 rounded-lg border border-red-800">
                        <div class="font-medium">Terjadi kesalahan validasi:</div>
                        <ul id="error-list" class="mt-2 text-sm list-disc list-inside"></ul>
                    </div>
                </div>

                <!-- Personal Information -->
                <x-profile.section title="Informasi Personal">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <x-profile.editable-field-enhanced label="Alamat" name="address" :value="$profile->address" />
                        <x-profile.editable-field-enhanced label="Provinsi" name="province" :value="$profile->province"
                            type="select" />
                        <x-profile.editable-field-enhanced label="Kota" name="city" :value="$profile->city" type="select" />
                        <x-profile.editable-field-enhanced label="Nomor Telepon" name="phone" :value="$profile->phone" />
                    </div>
                </x-profile.section>

                <!-- Additional Information -->
                <x-profile.section title="Informasi Tambahan">
                    <div class="space-y-6">
                        <x-profile.editable-field-enhanced label="Hobi" name="hobbies" :value="$profile->hobbies" />
                        <x-profile.editable-field-enhanced label="Bio" name="bio" :value="$profile->bio"
                            type="textarea" />
                    </div>
                </x-profile.section>

                <!-- Save Button -->
                <div id="save-button" class="hidden mt-4">
                    <button type="submit"
                        class="w-full md:w-auto px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700
                        transition duration-200 flex items-center justify-center shadow-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Simpan Semua Perubahan
                    </button>
                </div>
            </form>

            <!-- Account Statistics -->
            <div class="p-6 pt-0">
                <h2 class="text-xl font-semibold text-indigo-300 mb-4">Statistik Akun</h2>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <x-profile.stats-card title="Total Postingan" :value="$user->posts->count()">
                        <x-slot:icon>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                            </svg>
                        </x-slot:icon>
                    </x-profile.stats-card>

                    <x-profile.stats-card title="Total Komentar" :value="$user->comments->count()">
                        <x-slot:icon>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                            </svg>
                        </x-slot:icon>
                    </x-profile.stats-card>

                    <x-profile.stats-card title="Bergabung Sejak" :value="$user->created_at->diffForHumans()">
                        <x-slot:icon>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </x-slot:icon>
                        <x-slot:footer>
                            <div class="flex items-center justify-between">
                                <span>Tanggal daftar:</span>
                                <span>{{ $user->created_at->format('d M Y') }}</span>
                            </div>
                        </x-slot:footer>
                    </x-profile.stats-card>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                initializeProvinceCity();

                const form = document.getElementById('profile-form');
                const validationErrors = document.getElementById('validation-errors');
                const errorList = document.getElementById('error-list');

                form.addEventListener('submit', async (e) => {
                    e.preventDefault();

                    const formFields = ['address', 'province', 'city', 'phone', 'hobbies', 'bio'];
                    let allFieldsValid = true;

                    formFields.forEach(field => {
                        const input = form.querySelector(`[name="${field}"]`);
                        if (!input.value.trim()) {
                            allFieldsValid = false;

                            const fieldContainer = input.closest('.editable-field');
                            const displayValue = fieldContainer.querySelector('.display-value');
                            const editField = fieldContainer.querySelector('.edit-field');

                            displayValue.classList.add('hidden');
                            editField.classList.remove('hidden');

                            input.classList.add('border-red-500');
                        }
                    });

                    if (!allFieldsValid) {
                        validationErrors.classList.remove('hidden');
                        errorList.innerHTML = '<li>Semua field harus diisi</li>';
                        return;
                    }

                    try {
                        const formData = new FormData(form);

                        validationErrors.classList.add('hidden');
                        errorList.innerHTML = '';

                        const response = await fetch('{{ route('profile.update') }}', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest',
                                'Accept': 'application/json'
                            }
                        });

                        if (!response.ok) {
                            const data = await response.json();

                            if (data.errors) {
                                validationErrors.classList.remove('hidden');
                                errorList.innerHTML = '';

                                Object.keys(data.errors).forEach(field => {
                                    data.errors[field].forEach(message => {
                                        errorList.innerHTML += `<li>${message}</li>`;
                                    });

                                    const input = form.querySelector(`[name="${field}"]`);
                                    if (input) {
                                        input.classList.add('border-red-500');
                                    }
                                });
                                return;
                            }
                        }

                        const data = await response.json();

                        if (data.success) {
                            document.getElementById('save-button').classList.add('hidden');

                            form.querySelectorAll('input, select, textarea').forEach(input => {
                                input.classList.remove('border-red-500');
                            });

                            Swal.fire({
                                title: 'Berhasil!',
                                text: data.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: data.message || 'Terjadi kesalahan saat menyimpan perubahan.',
                                icon: 'error'
                            });
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menyimpan perubahan.',
                            icon: 'error'
                        });
                    }
                });

                async function initializeProvinceCity() {
                    console.log('Initializing province/city selects');
                    const provinceSelect = document.querySelector('select[name="province"]');
                    const citySelect = document.querySelector('select[name="city"]');

                    if (!provinceSelect || !citySelect) {
                        console.log('Select elements not found.');
                        return;
                    }

                    try {
                        console.log('Fetching provinces...');
                        const response = await fetch(
                            'https://www.emsifa.com/api-wilayah-indonesia/api/provinces.json');
                        const provinces = await response.json();
                        console.log('Provinces loaded:', provinces.length);

                        provinceSelect.innerHTML = '<option value="">Pilih Provinsi</option>';
                        provinces.forEach(province => {
                            const option = document.createElement('option');
                            option.value = province.name;
                            option.textContent = province.name;
                            option.dataset.id = province.id;
                            provinceSelect.appendChild(option);
                        });

                        const currentProvince = '{{ $profile->province }}';
                        console.log('Current province:', currentProvince);
                        if (currentProvince) {
                            provinceSelect.value = currentProvince;

                            const selectedOption = Array.from(provinceSelect.options).find(opt => opt.value ===
                                currentProvince);
                            if (selectedOption && selectedOption.dataset.id) {
                                const provinceId = selectedOption.dataset.id;
                                console.log('Found province ID:', provinceId);
                                loadCities(provinceId, '{{ $profile->city }}');
                            }
                        }

                        provinceSelect.addEventListener('change', async function() {
                            const selectedOption = this.options[this.selectedIndex];
                            const provinceId = selectedOption.dataset.id;
                            console.log('Province changed, selected ID:', provinceId);

                            if (!provinceId) return;

                            loadCities(provinceId);
                        });
                    } catch (error) {
                        console.error('Error fetching provinces:', error);
                    }
                }

                async function loadCities(provinceId, currentCity = '') {
                    console.log('Loading cities for province ID:', provinceId);
                    const citySelect = document.querySelector('select[name="city"]');
                    if (!citySelect) return;

                    try {
                        const response = await fetch(
                            `https://www.emsifa.com/api-wilayah-indonesia/api/regencies/${provinceId}.json`);
                        const cities = await response.json();
                        console.log('Cities loaded:', cities.length);

                        citySelect.innerHTML = '<option value="">Pilih Kota</option>';
                        cities.forEach(city => {
                            const option = document.createElement('option');
                            option.value = city.name;
                            option.textContent = city.name;
                            citySelect.appendChild(option);
                        });

                        if (currentCity) {
                            console.log('Setting current city:', currentCity);
                            citySelect.value = currentCity;
                        }
                    } catch (error) {
                        console.error('Error fetching cities:', error);
                    }
                }

                document.querySelectorAll('.edit-button').forEach(button => {
                    if (button.closest('.editable-field').querySelector('select[name="province"]')) {
                        button.addEventListener('click', () => {
                            console.log('Province field clicked, initializing selects');
                            setTimeout(initializeProvinceCity, 100);
                        });
                    }
                });

                form.querySelectorAll('input, select, textarea').forEach(input => {
                    input.addEventListener('focus', function() {
                        this.classList.remove('border-red-500');
                    });
                });
            });
        </script>
    @endpush
@endsection
