@extends('layouts.homeLayout')

@section('title', 'Beranda')

@section('content')
    <section class="hero" id="home" aria-label="home">
        <div class="container">
            <div class="hero-content">
                <p class="hero-subtitle">Ketua Umum PK IMM Alqossam</p>

                <h1 class="headline headline-1 section-title">
                    Khus<PERSON>l <span class="span">Ma'arif</span>
                </h1>

                <p class="hero-text">
                    Selamat Datang di Website PK IMM Al-Qossam FAI UMSurabaya <br />

                    PK IMM Al-Qossam adalah organisasi mahasiswa FAI UMSurabaya yang berdiri sejak 1990.
                    Bergerak di bawah Fakultas Agama Islam dengan tagline "Kader Profetik".
                </p>

                <div class="input-wrapper">
                    <input type="email" name="email_address" placeholder="Type your email address" required
                        class="input-field" autocomplete="off" />

                    <a href="{{ route('login') }}" class="btn btn-primary">
                        <span class="span">Bergabung</span>

                        <ion-icon name="arrow-forward-outline" aria-hidden="true"></ion-icon>
                    </a>
                </div>
            </div>

            <div class="hero-banner">
                <img src="{{ asset('home/assets/images/Maarif.png') }}" width="327" height="490" alt="Wren Clark"
                    class="w-100" />

                <img src="{{ asset('home/assets/images/pattern-2.svg') }}" width="27" height="26" alt="shape"
                    class="shape shape-1" />

                <img src="{{ asset('home/assets/images/pattern-3.svg') }}" width="27" height="26" alt="shape"
                    class="shape shape-2" />
            </div>

            <img src="{{ asset('home/assets/images/shadow-1.svg') }}" width="500" height="800" alt="Shadow 1"
                class="hero-bg hero-bg-1" />

            <img src="{{ asset('home/assets/images/shadow-2.svg') }}" width="500" height="500" alt="Shadow 2"
                class="hero-bg hero-bg-2" />
        </div>
    </section>

    <section class="topics" id="topics" aria-labelledby="topic-label">
        <div class="container">
            <div class="card topic-card" style="position: relative;">
                <!-- Konten yang sudah ada -->
                <div class="card-content">
                    <h2 class="headline headline-2 section-title card-title" id="topic-label">
                        Bidang-bidang IMM Al-Qossam
                    </h2>

                    @php
                        $organizationCount = App\Models\OrganizationStructure::count();
                    @endphp
                    <p class="card-text">
                        Ada {{ $organizationCount }} bidang di PK IMM Alqossam diantaranya adalah....
                    </p>

                    <div class="btn-group">
                        <button class="btn-icon" aria-label="previous" data-slider-prev>
                            <ion-icon name="arrow-back" aria-hidden="true"></ion-icon>
                        </button>

                        <button class="btn-icon" aria-label="next" data-slider-next>
                            <ion-icon name="arrow-forward" aria-hidden="true"></ion-icon>
                        </button>
                    </div>
                </div>

                <div class="slider" data-slider>
                    <x-slider.BidangSlider :items="$bidangSliderItems" />
                </div>
            </div>
        </div>
    </section>

    <section class="section feature" aria-label="feature" id="featured">
        <div class="container">
            <h2 class="headline headline-2 section-title">
                <span class="span">Tulisan Terpopuler</span>
            </h2>

            <p class="section-text">Tulisan dengan peminat pembaca terbanyak</p>

            <x-feature.FeatureList :items="$featuredPosts" />

            <a href="{{ route('posts.all') }}" class="btn btn-secondary">
                <span class="span">Lihat Selengkapnya</span>
                <ion-icon name="arrow-forward" aria-hidden="true"></ion-icon>
            </a>
        </div>
    </section>

    <section class="tags" aria-labelledby="tag-label">
        <div class="container">
            <h2 class="headline headline-2 section-title" id="tag-label">
                <span class="span">Tag Terpopuler</span>
            </h2>

            <p class="section-text">Kata kunci yang paling banyak dicari</p>

            <x-tags.PopularTags :tags="$popularTags" />
        </div>
    </section>

    <section class="section recent-post" id="recent" aria-labelledby="recent-label">
        <div class="container">
            <div class="post-main">
                <h2 class="headline headline-2 section-title">
                    <span class="span">Postingan Terbaru</span>
                </h2>

                <p class="section-text">Jangan lewatkan tren terbaru</p>

                <x-recent.RecentPostList :posts="$recentPosts" />
            </div>

            <div class="post-aside grid-list">
                <div class="card aside-card">
                    <h3 class="headline headline-2 aside-title">
                        <span class="span">Postingan Populer</span>
                    </h3>

                    <x-popular.PopularPostList :posts="$popularPosts" />
                </div>

                <div class="card aside-card">
                    <h3 class="headline headline-2 aside-title">
                        <span class="span">Komentar Terakhir</span>
                    </h3>

                    @if (isset($latestComments) && count($latestComments) > 0)
                        <x-comment.CommentList :comments="$latestComments" />
                    @else
                        @php
                            $comments = [
                                [
                                    'text' =>
                                        '" Gosh jaguar ostrich quail one excited dear hello and bound and the and bland moral misheard roadrunner "',
                                    'img' => 'author-6.png',
                                    'author' => 'Jane Cooper',
                                    'datetime' => '2022-04-15',
                                    'date' => '15 April 2022',
                                ],
                                [
                                    'text' =>
                                        '" Gosh jaguar ostrich quail one excited dear hello and bound and the and bland moral misheard roadrunner "',
                                    'img' => 'author-7.png',
                                    'author' => 'Katen Doe',
                                    'datetime' => '2022-04-15',
                                    'date' => '15 April 2022',
                                ],
                                [
                                    'text' =>
                                        '" Gosh jaguar ostrich quail one excited dear hello and bound and the and bland moral misheard roadrunner "',
                                    'img' => 'author-8.png',
                                    'author' => 'Barbara Cartland',
                                    'datetime' => '2022-04-15',
                                    'date' => '15 April 2022',
                                ],
                            ];
                        @endphp
                        <x-comment.CommentList :comments="$comments" />
                    @endif
                </div>

                <div class="card aside-card insta-card">
                    <a href="#" class="logo">
                        <x-logo.logo width="119" height="37" />
                    </a>

                    <p class="card-text">Ikuti Instagram Kami</p>

                    @php
                        $instaItems = [
                            ['img' => 'insta-post-1.png', 'alt' => 'insta post 1'],
                            ['img' => 'insta-post-2.png', 'alt' => 'insta post 2'],
                            ['img' => 'insta-post-3.png', 'alt' => 'insta post 3'],
                            ['img' => 'insta-post-4.png', 'alt' => 'insta post 4'],
                            ['img' => 'insta-post-5.png', 'alt' => 'insta post 5'],
                            ['img' => 'insta-post-6.png', 'alt' => 'insta post 6'],
                            ['img' => 'insta-post-7.png', 'alt' => 'insta post 7'],
                            ['img' => 'insta-post-8.png', 'alt' => 'insta post 8'],
                            ['img' => 'insta-post-9.png', 'alt' => 'insta post 9'],
                        ];
                    @endphp
                    <x-insta.InstaList :items="$instaItems" />
                </div>
            </div>
        </div>
    </section>
@endsection
