<?php
    $links = [
        ['href' => route('app.dashboard'), 'label' => 'Dashboard', 'can' => 'dashboard-user'],
        ['href' => route('posts.index'), 'label' => 'Postingan', 'can' => 'dashboard-user'],
        ['href' => route('organization.index'), 'label' => 'Struktural', 'can' => 'dashboard-admin'],
    ];
?>

<div class="flex flex-col space-y-1 sm:flex-row sm:space-x-4 sm:space-y-0">
    <?php $__currentLoopData = $links; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $link): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check($link['can'])): ?>
            <a href="<?php echo e($link['href']); ?>"
                class="rounded-md px-3 py-2 text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white
               <?php echo e(request()->url() == $link['href'] ? 'bg-gray-900 text-white' : ''); ?>"
                aria-current="<?php echo e(request()->url() == $link['href'] ? 'page' : false); ?>">
                <?php echo e($link['label']); ?>

            </a>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/components/navbar/NavbarLinks.blade.php ENDPATH**/ ?>