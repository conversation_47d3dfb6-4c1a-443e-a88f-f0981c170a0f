@props(['title', 'chartId', 'description' => null, 'multiLine' => false])

<div {{ $attributes->merge(['class' => 'p-6 bg-gray-800 rounded-xl shadow-lg border border-gray-700']) }}>
    <div class="flex items-center justify-between mb-6 flex-wrap">
        <div>
            <h3 class="text-lg font-semibold text-indigo-300">{{ $title }}</h3>
            @if ($description)
                <p class="text-gray-400 text-sm mt-1">{{ $description }}</p>
            @endif
        </div>
        <div class="flex space-x-2 mt-2 sm:mt-0">
            <button id="monthlyFilter"
                class="chart-filter active px-3 py-1 text-xs rounded-full bg-indigo-700 text-indigo-100 hover:bg-indigo-600 transition">
                Bulan Ini
            </button>
            <button id="quarterlyFilter"
                class="chart-filter px-3 py-1 text-xs rounded-full bg-gray-700 text-gray-300 hover:bg-indigo-700 hover:text-indigo-100 transition">
                Triwulan
            </button>
            <button id="yearlyFilter"
                class="chart-filter px-3 py-1 text-xs rounded-full bg-gray-700 text-gray-300 hover:bg-indigo-700 hover:text-indigo-100 transition">
                Tahunan
            </button>
        </div>
    </div>
    <div style="height: 300px;" class="chart-container relative">
        <div
            class="absolute inset-0 flex items-center justify-center chart-loading opacity-0 pointer-events-none transition-opacity">
            <div class="w-10 h-10 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
        <canvas id="{{ $chartId }}" class="transition-opacity duration-500"></canvas>
    </div>
    <div id="topPostsInfo" class="mt-4 text-sm text-gray-300 bg-gray-700/50 p-3 rounded-lg">
        <h4 class="font-medium text-indigo-300 mb-2">Postingan Terbaik:</h4>
        <div id="topViewsPost" class="mb-1">
            <span class="inline-block w-3 h-3 rounded-full bg-indigo-500 mr-2"></span>
            <span class="font-medium">Pengunjung Terbanyak:</span> <span class="text-gray-400">Memuat data...</span>
        </div>
        <div id="topCommentsPost" class="mb-1">
            <span class="inline-block w-3 h-3 rounded-full bg-pink-500 mr-2"></span>
            <span class="font-medium">Komentar Terbanyak:</span> <span class="text-gray-400">Memuat data...</span>
        </div>
        <div id="topLikesPost">
            <span class="inline-block w-3 h-3 rounded-full bg-green-500 mr-2"></span>
            <span class="font-medium">Like Terbanyak:</span> <span class="text-gray-400">Memuat data...</span>
        </div>
    </div>
</div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chartContainer = document.getElementById('{{ $chartId }}').closest('.chart-container');
            const chartLoading = chartContainer.querySelector('.chart-loading');
            const filters = chartContainer.parentElement.querySelectorAll('.chart-filter');

            filters.forEach(filter => {
                filter.addEventListener('click', function() {
                    chartLoading.classList.remove('opacity-0');
                    chartLoading.classList.add('opacity-100');

                    filters.forEach(f => f.classList.remove('active', 'bg-indigo-700',
                        'text-indigo-100'));
                    filters.forEach(f => f.classList.add('bg-gray-700', 'text-gray-300'));
                    this.classList.add('active', 'bg-indigo-700', 'text-indigo-100');
                    this.classList.remove('bg-gray-700', 'text-gray-300');

                    setTimeout(() => {
                        chartLoading.classList.remove('opacity-100');
                        chartLoading.classList.add('opacity-0');
                    }, 800);
                });
            });
        });
    </script>
</div>
