<div
    {{ $attributes->merge(['class' => 'bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg p-4 shadow-lg border border-gray-700 hover:border-indigo-500 transition-all duration-300 transform hover:-translate-y-1']) }}>
    <div class="flex items-center">
        @if (isset($icon))
            <div class="mr-3 text-indigo-400">
                {{ $icon }}
            </div>
        @endif
        <div>
            <div class="text-gray-400 text-sm">{{ $title }}</div>
            <div class="text-2xl font-bold text-indigo-300">{{ $value }}</div>
        </div>
    </div>

    @if (isset($footer))
        <div class="mt-2 pt-2 border-t border-gray-700 text-xs text-gray-400">
            {{ $footer }}
        </div>
    @endif
</div>
