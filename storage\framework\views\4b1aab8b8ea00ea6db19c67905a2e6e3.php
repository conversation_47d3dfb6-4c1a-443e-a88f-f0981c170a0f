<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name' => '',
    'id' => null,
    'label' => '',
    'placeholder' => '',
    'value' => '',
    'required' => false,
    'disabled' => false,
    'error' => null,
    'helper' => null,
    'rows' => 4,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name' => '',
    'id' => null,
    'label' => '',
    'placeholder' => '',
    'value' => '',
    'required' => false,
    'disabled' => false,
    'error' => null,
    'helper' => null,
    'rows' => 4,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $inputId = $id ?? $name;
?>

<div class="mb-4">
    <?php if($label): ?>
        <label for="<?php echo e($inputId); ?>" class="block mb-2 text-sm font-medium text-indigo-200">
            <?php echo e($label); ?>

            <?php if($required): ?>
                <span class="text-red-400">*</span>
            <?php endif; ?>
        </label>
    <?php endif; ?>

    <div class="relative">
        <textarea name="<?php echo e($name); ?>" id="<?php echo e($inputId); ?>" rows="<?php echo e($rows); ?>" placeholder="<?php echo e($placeholder); ?>"
            <?php echo e($required ? 'required' : ''); ?> <?php echo e($disabled ? 'disabled' : ''); ?>

            <?php echo e($attributes->merge([
                'class' =>
                    'block w-full px-4 py-2.5 bg-gray-700 border ' .
                    ($error
                        ? 'border-red-500 focus:ring-red-500'
                        : 'border-gray-600 focus:border-indigo-500 focus:ring-indigo-500') .
                    ' rounded-lg text-sm text-indigo-100 focus:outline-none focus:ring-1 shadow-sm',
            ])); ?>><?php echo e($value); ?></textarea>
    </div>

    <?php if($error): ?>
        <p class="mt-1 text-sm text-red-500"><?php echo e($error); ?></p>
    <?php elseif($helper): ?>
        <p class="mt-1 text-sm text-gray-400"><?php echo e($helper); ?></p>
    <?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/components/form/textarea.blade.php ENDPATH**/ ?>