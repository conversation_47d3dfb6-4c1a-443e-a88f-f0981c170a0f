@php
    $user = Auth::user();
    if ($user) {
        $profile = $user->profile;
        $avatarUrl = $profile && $profile->avatar
            ? Storage::url($profile->avatar)
            : 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&background=6366f1&color=fff';
    }
@endphp

<nav class="bg-gray-800">
    <div class="mx-auto max-w-7xl px-2 sm:px-6 lg:px-8">
        <div class="relative flex h-16 items-center justify-between">
            <div class="absolute inset-y-0 left-0 flex items-center sm:hidden">
                <button type="button"
                    class="relative inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-700 hover:text-white focus:ring-2 focus:ring-white focus:outline-none focus:ring-inset"
                    aria-controls="mobile-menu" aria-expanded="false" onclick="toggleMobileMenu()">
                    <span class="absolute -inset-0.5"></span>
                    <span class="sr-only">Open main menu</span>
                    <svg class="block size-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" aria-hidden="true" id="menu-open-icon">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                    </svg>
                    <svg class="hidden size-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" aria-hidden="true" id="menu-close-icon">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex flex-1 items-center justify-center sm:items-stretch sm:justify-start">
                <div class="flex shrink-0 items-center">
                    <a href="{{ route('home') }}">
                        <x-logo.IMM width="8" height="8" />
                    </a>
                </div>
                <div class="hidden sm:ml-6 sm:block">
                    <x-navbar.NavbarLinks />
                </div>
            </div>
            <div class="absolute inset-y-0 right-0 flex items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0">
                @auth
                    @php
                        $notifications = $user->notifications()->orderBy('created_at', 'desc')->get();
                    @endphp
                    <x-navbar.NotificationDropdown :notifications="$notifications" />
                    <x-navbar.ProfileDropdown :user="$user" :avatar-url="$avatarUrl" />
                @endauth
            </div>
        </div>
    </div>
    <div class="sm:hidden hidden" id="mobile-menu">
        <div class="space-y-1 px-2 pt-2 pb-3">
            <x-navbar.NavbarLinks />
        </div>
    </div>
</nav>

<script>
    function toggleMobileMenu() {
        const mobileMenu = document.getElementById('mobile-menu');
        const menuOpenIcon = document.getElementById('menu-open-icon');
        const menuCloseIcon = document.getElementById('menu-close-icon');

        mobileMenu.classList.toggle('hidden');
        menuOpenIcon.classList.toggle('hidden');
        menuCloseIcon.classList.toggle('hidden');
    }

    function closeOtherDropdowns(currentDropdownId) {
        const dropdowns = ['notification-dropdown', 'profile-dropdown'];
        dropdowns.forEach(id => {
            if (id !== currentDropdownId) {
                const dropdown = document.getElementById(id);
                if (dropdown && !dropdown.classList.contains('hidden')) {
                    dropdown.classList.add('hidden');
                }
            }
        });
    }

    document.addEventListener('click', function(event) {
        const notificationButton = document.getElementById('notification-button');
        const profileButton = document.getElementById('user-menu-button');

        if (notificationButton && !notificationButton.contains(event.target)) {
            const notificationDropdown = document.getElementById('notification-dropdown');
            if (notificationDropdown && !notificationDropdown.contains(event.target)) {
                notificationDropdown.classList.add('hidden');
            }
        }

        if (profileButton && !profileButton.contains(event.target)) {
            const profileDropdown = document.getElementById('profile-dropdown');
            if (profileDropdown && !profileDropdown.contains(event.target)) {
                profileDropdown.classList.add('hidden');
            }
        }
    });
</script>

