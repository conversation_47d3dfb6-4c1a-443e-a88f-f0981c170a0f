<div
    <?php echo e($attributes->merge(['class' => 'bg-gradient-to-br from-gray-800 to-gray-900 rounded-lg p-4 shadow-lg border border-gray-700 hover:border-indigo-500 transition-all duration-300 transform hover:-translate-y-1'])); ?>>
    <div class="flex items-center">
        <?php if(isset($icon)): ?>
            <div class="mr-3 text-indigo-400">
                <?php echo e($icon); ?>

            </div>
        <?php endif; ?>
        <div>
            <div class="text-gray-400 text-sm"><?php echo e($title); ?></div>
            <div class="text-2xl font-bold text-indigo-300"><?php echo e($value); ?></div>
        </div>
    </div>

    <?php if(isset($footer)): ?>
        <div class="mt-2 pt-2 border-t border-gray-700 text-xs text-gray-400">
            <?php echo e($footer); ?>

        </div>
    <?php endif; ?>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/components/profile/stats-card.blade.php ENDPATH**/ ?>