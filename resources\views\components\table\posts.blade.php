@props(['posts'])

<div class="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
    <!-- Desktop Table -->
    <div class="hidden md:block">
        <table class="min-w-full divide-y divide-gray-700">
            <thead class="bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-indigo-200 uppercase tracking-wider">Judul</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-indigo-200 uppercase tracking-wider">Kategori</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-indigo-200 uppercase tracking-wider">Tanggal</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-indigo-200 uppercase tracking-wider">Aksi</th>
                </tr>
            </thead>
            <tbody class="bg-gray-800 divide-y divide-gray-700">
                @foreach ($posts as $post)
                <tr class="hover:bg-gray-700 transition duration-150">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <img class="h-10 w-10 rounded-full object-cover" src="{{ asset('storage/' . $post->image) }}" alt="{{ $post->title }}">
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-indigo-200">{{ $post->title }}</div>
                                <div class="text-sm text-indigo-100">{{ Str::limit($post->description, 50) }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-indigo-900 text-indigo-100">
                            {{ $post->category->title }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-indigo-100">
                        {{ $post->created_at->format('d M Y') }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex gap-3">
                            <a href="{{ route('posts.edit', $post->id) }}" class="text-indigo-400 hover:text-indigo-300">
                                <i class="fas fa-edit"></i>
                            </a>
                            <button
                                onclick="confirmDelete('{{ $post->id }}', '{{ $post->title }}')"
                                class="text-red-400 hover:text-red-300"
                            >
                                <i class="fas fa-trash"></i>
                            </button>
                            <form id="delete-form-{{ $post->id }}" action="{{ route('posts.destroy', $post->id) }}" method="POST" class="hidden">
                                @csrf
                                @method('DELETE')
                            </form>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Mobile Cards -->
    <div class="md:hidden space-y-4 p-4">
        @foreach ($posts as $post)
        <div class="bg-gray-700 rounded-lg p-4 shadow">
            <div class="flex items-center space-x-3">
                <img class="h-12 w-12 rounded-full object-cover" src="{{ asset('storage/' . $post->image) }}" alt="{{ $post->title }}">
                <div>
                    <h3 class="text-sm font-medium text-indigo-200">{{ $post->title }}</h3>
                    <p class="text-xs text-indigo-100">{{ Str::limit($post->description, 30) }}</p>
                </div>
            </div>
            <div class="mt-3 flex justify-between items-center">
                <span class="px-2 text-xs font-medium rounded-full bg-indigo-900 text-indigo-100">
                    {{ $post->category->title }}
                </span>
                <span class="text-xs text-indigo-100">
                    {{ $post->created_at->format('d M Y') }}
                </span>
            </div>
            <div class="mt-3 flex justify-end space-x-3">
                <a href="{{ route('posts.edit', $post->id) }}" class="text-indigo-400 hover:text-indigo-300 text-sm">
                    <i class="fas fa-edit"></i> Edit
                </a>
                <button
                    onclick="confirmDelete('{{ $post->id }}', '{{ $post->title }}')"
                    class="text-red-400 hover:text-red-300 text-sm"
                >
                    <i class="fas fa-trash"></i> Hapus
                </button>
                <form id="delete-form-{{ $post->id }}" action="{{ route('posts.destroy', $post->id) }}" method="POST" class="hidden">
                    @csrf
                    @method('DELETE')
                </form>
            </div>
        </div>
        @endforeach
    </div>
</div>

<script>
function confirmDelete(postId, postTitle) {
    Swal.fire({
        title: 'Apakah Anda yakin?',
        text: `Postingan "${postTitle}" akan dihapus secara permanen!`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6366f1',
        confirmButtonText: 'Ya, hapus!',
        cancelButtonText: 'Batal',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {
            document.getElementById(`delete-form-${postId}`).submit();
        }
    });
}

@if(session('success'))
    Swal.fire({
        icon: 'success',
        title: 'Berhasil!',
        text: '{{ session('success') }}',
        timer: 3000,
        showConfirmButton: false
    });
@endif
</script>

