@props(['name', 'label', 'options' => [], 'selected' => [], 'grid' => true])

<div class="mb-6">
    <label class="block text-sm font-medium text-indigo-100 mb-3">{{ $label }}</label>
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
        @foreach($options as $option)
            <label class="block">
                <input
                    type="checkbox"
                    name="{{ $name }}[]"
                    value="{{ $option['id'] ?? $option['value'] }}"
                    @checked(in_array($option['id'] ?? $option['value'], old($name, $selected)))
                    class="hidden peer"
                >
                <div class="p-3 bg-gray-800 rounded-lg border border-gray-700 hover:border-indigo-400 peer-checked:border-indigo-500 peer-checked:bg-indigo-900/30 transition duration-200">
                    @isset($option['image'])
                        <img src="{{ asset('storage/'.$option['image']) }}" alt="{{ $option['name'] }}" class="w-full h-20 object-cover rounded mb-2">
                    @endisset
                    <span class="text-indigo-100 font-medium">{{ $option['name'] ?? $option['label'] }}</span>
                    @isset($option['description'])
                        <span class="text-gray-400 text-xs mt-1 block">{{ $option['description'] }}</span>
                    @endisset
                </div>
            </label>
        @endforeach
    </div>
    @error($name)
        <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
    @enderror
</div>
