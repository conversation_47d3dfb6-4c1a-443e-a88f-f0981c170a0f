<div class="relative h-48 sm:h-64 bg-gradient-to-r from-indigo-800 to-purple-700 overflow-hidden">
    <!-- Decorative elements -->
    <div class="absolute inset-0 opacity-20">
        <div class="absolute top-10 left-10 w-20 h-20 rounded-full bg-white"></div>
        <div class="absolute top-40 right-20 w-12 h-12 rounded-full bg-indigo-300"></div>
        <div class="absolute bottom-5 left-1/4 w-16 h-16 rounded-full bg-purple-400"></div>
    </div>

    <!-- Profile header content -->
    <div class="absolute bottom-0 left-0 right-0 px-6 py-4 bg-gradient-to-t from-black/80 to-transparent">
        <div class="flex items-center space-x-4">
            {{ $slot }}

            <div class="flex-1">
                <h1 class="text-2xl font-bold text-white">{{ $name }}</h1>
                <p class="text-indigo-200">{{ $email }}</p>
            </div>

            @if (isset($actions))
                <div class="hidden sm:flex">
                    {{ $actions }}
                </div>
            @endif
        </div>
    </div>
</div>
