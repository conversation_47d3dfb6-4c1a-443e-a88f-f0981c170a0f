@props(['user'])

<div
    {{ $attributes->merge(['class' => 'relative mb-6 p-8 bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 rounded-xl shadow-[0_10px_20px_rgba(79,70,229,0.4)] overflow-hidden']) }}>
    <div class="absolute top-0 right-0 w-64 h-64 bg-white opacity-10 rounded-full -mt-20 -mr-20"></div>
    <div class="absolute bottom-0 left-0 w-40 h-40 bg-white opacity-10 rounded-full -mb-16 -ml-16"></div>

    <div class="relative flex items-center">
        <div class="mr-6">
            <div
                class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-white text-2xl overflow-hidden">
                @if ($user->profile && $user->profile->avatar)
                    <img src="{{ asset('storage/' . $user->profile->avatar) }}" alt="{{ $user->name }}"
                        class="w-full h-full object-cover">
                @else
                    {{ substr($user->name, 0, 1) }}
                @endif
            </div>
        </div>
        <div>
            <p class="text-indigo-200 text-sm mb-1">
                <span id="greeting"></span>
            </p>
            <h1 class="text-3xl font-bold mb-2 text-white">{{ $user->name }}</h1>
            <div class="flex items-center">
                <span class="font-mono px-3 py-1 rounded-full bg-black bg-opacity-30 text-indigo-100 text-xs mr-2">
                    {{ ucfirst($user->role) }}
                </span>
                <span class="text-xs text-indigo-200">Terakhir Login: {{ now()->format('d M Y, H:i') }}</span>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const hour = new Date().getHours();
            const greetingElement = document.getElementById('greeting');

            let greeting = '';
            if (hour >= 5 && hour < 12) {
                greeting = 'Selamat Pagi';
            } else if (hour >= 12 && hour < 15) {
                greeting = 'Selamat Siang';
            } else if (hour >= 15 && hour < 18) {
                greeting = 'Selamat Sore';
            } else {
                greeting = 'Selamat Malam';
            }

            greetingElement.textContent = greeting;
        });
    </script>
</div>
