<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Post;
use App\Models\Tag;
use App\Models\PostUserReaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use App\Notifications\LikeNotification;
use App\Notifications\DislikeNotification;

class PostController extends Controller
{
    public function index(Request $request)
    {
        $search = $request->input('search');

        $query = Post::query()->with(['category', 'user', 'comments']);
        $pendingPosts = Post::where('is_published', false)->latest()->get();

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhere('content', 'like', "%{$search}%");
            });
        }

        if (Auth::user()->role !== 'admin') {
            $query->where('user_id', Auth::id());
        }

        $query->latest();

        $posts = $query->paginate(9);

        return view('app.posts.index', compact('posts', 'search', 'pendingPosts'));
    }

    public function create()
    {
        $categories = Category::all();
        $tags = Tag::all();
        $selectedTags = [];
        return view('app.posts.create', compact('categories', 'tags', 'selectedTags'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'content' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'tags' => 'nullable|array|max:3',
            'tags.*' => 'exists:tags,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $post = Post::create([
            'title' => $request->title,
            'slug' => str_replace(' ', '-', strtolower($request->title)),
            'description' => $request->description,
            'content' => $request->content,
            'category_id' => $request->category_id,
            'user_id' => Auth::id(),
            'image' => $request->file('image') ? $request->file('image')->store('posts', 'public') : null,
        ]);

        if ($request->tags) {
            $post->tags()->attach(array_slice($request->tags, 0, 3));
        }

        return redirect()->route('posts.index')->with('success', 'Postingan berhasil dibuat!');
    }

    public function show($slug)
    {
        $post = Post::where('slug', $slug)
            ->with([
                'category',
                'user',
                'tags',
                'comments.user',
                'comments.replies.user',
                'userReaction' => function ($query) {
                    $query->where('user_id', Auth::id());
                }
            ])
            ->firstOrFail();

        if (!$post->is_published && (!Auth::check() || Auth::user()->role !== 'admin')) {
            abort(403, 'Postingan ini masih menunggu persetujuan admin');
        }

        $post->increment('views');

        $relatedPosts = Post::where('id', '!=', $post->id)
            ->where(function ($query) use ($post) {
                $query->where('category_id', $post->category_id)
                    ->orWhereHas('tags', function ($q) use ($post) {
                        $postTagIds = $post->tags->pluck('id')->toArray();
                        $q->whereIn('tags.id', $postTagIds);
                    });
            })
            ->with(['user', 'category'])
            ->limit(3)
            ->get();

        return view('post.show', compact('post', 'relatedPosts'));
    }


    public function allPosts()
    {
        $posts = Post::with([
            'category',
            'tags',
            'comments'
        ])
            ->latest()
            ->paginate(12);

        // dd($posts);
        return view('posts.all', compact('posts'));
    }

    public function edit(Post $post)
    {
        if (Auth::user()->role !== 'admin' && $post->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $categories = Category::all();
        $tags = Tag::all();
        $selectedTags = $post->tags->pluck('id')->toArray();

        return view('app.posts.edit', compact('post', 'categories', 'tags', 'selectedTags'));
    }

    public function update(Request $request, Post $post)
    {
        if (Auth::user()->role !== 'admin' && $post->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'content' => 'required|string',
            'category_id' => 'required|exists:categories,id',
            'tags' => 'nullable|array|max:3',
            'tags.*' => 'exists:tags,id',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = [
            'title' => $request->title,
            'slug' => str_replace(' ', '-', strtolower($request->title)),
            'description' => $request->description,
            'content' => $request->content,
            'category_id' => $request->category_id,
        ];

        if ($request->hasFile('image')) {
            if ($post->image) {
                Storage::disk('public')->delete($post->image);
            }
            $data['image'] = $request->file('image')->store('posts', 'public');
        }

        $post->update($data);

        if ($request->tags) {
            $post->tags()->sync(array_slice($request->tags, 0, 3));
        } else {
            $post->tags()->sync([]);
        }

        return redirect()->route('posts.index')->with('success', 'Postingan berhasil diperbarui!');
    }

    public function destroy(Post $post)
    {
        if (Auth::user()->role !== 'admin' && $post->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        if ($post->image) {
            Storage::disk('public')->delete($post->image);
        }

        $post->tags()->detach();

        $post->delete();

        return redirect()->route('posts.index')->with('success', 'Postingan berhasil dihapus!');
    }

    /**
     * Handle post like
     */
    public function like(Post $post)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $userId = Auth::id();
        $existingReaction = PostUserReaction::where('post_id', $post->id)
            ->where('user_id', $userId)
            ->first();

        DB::beginTransaction();
        try {
            if ($existingReaction) {
                if ($existingReaction->reaction_type === 'like') {
                    $existingReaction->delete();
                    $post->decrement('likes');
                } else {
                    $existingReaction->reaction_type = 'like';
                    $existingReaction->save();
                    $post->decrement('dislikes');
                    $post->increment('likes');
                }
            } else {
                PostUserReaction::create([
                    'post_id' => $post->id,
                    'user_id' => $userId,
                    'reaction_type' => 'like'
                ]);
                $post->increment('likes');

                if ($post->user_id !== $userId) {
                    $postOwner = $post->user;
                    $currentUser = Auth::user();
                    $postOwner->notify(new LikeNotification(
                        $post->id,
                        $post->title,
                        $currentUser->name
                    ));
                }
            }

            DB::commit();
            return redirect()->back();
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Terjadi kesalahan');
        }
    }

    /**
     * Handle post dislike
     */
    public function dislike(Post $post)
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $userId = Auth::id();
        $existingReaction = PostUserReaction::where('post_id', $post->id)
            ->where('user_id', $userId)
            ->first();

        DB::beginTransaction();
        try {
            if ($existingReaction) {
                if ($existingReaction->reaction_type === 'dislike') {
                    $existingReaction->delete();
                    $post->decrement('dislikes');
                } else {
                    $existingReaction->reaction_type = 'dislike';
                    $existingReaction->save();
                    $post->decrement('likes');
                    $post->increment('dislikes');
                }
            } else {
                PostUserReaction::create([
                    'post_id' => $post->id,
                    'user_id' => $userId,
                    'reaction_type' => 'dislike'
                ]);
                $post->increment('dislikes');

                if ($post->user_id !== $userId) {
                    $postOwner = $post->user;
                    $currentUser = Auth::user();
                    $postOwner->notify(new DislikeNotification(
                        $post->id,
                        $post->title,
                        $currentUser->name
                    ));
                }
            }

            DB::commit();
            return redirect()->back();
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Terjadi kesalahan');
        }
    }

    /**
     * Get user's reaction to a post
     */
    public function getUserReaction(Post $post)
    {
        if (!Auth::check()) {
            return response()->json(['reaction' => null]);
        }

        $userId = Auth::id();
        $reaction = PostUserReaction::where('post_id', $post->id)
            ->where('user_id', $userId)
            ->first();

        return response()->json([
            'reaction' => $reaction ? $reaction->reaction_type : null,
            'likes' => $post->likes,
            'dislikes' => $post->dislikes
        ]);
    }

    public function approvePost(Request $request, Post $post)
    {
        if (Auth::user()->role !== 'admin') {
            abort(403, 'Anda tidak memiliki akses untuk menyetujui postingan');
        }

        $post->update(['is_published' => true]);

        return back()->with('success', 'Postingan berhasil disetujui');
    }

    public function rejectPost(Request $request, Post $post)
    {
        if (Auth::user()->role !== 'admin') {
            abort(403, 'Anda tidak memiliki akses untuk menolak postingan');
        }
        $post->update(['is_published' => false]);
        return back()->with('success', 'Postingan berhasil ditolak');
    }
}
