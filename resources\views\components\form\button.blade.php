@props([
    'type' => 'button',
    'variant' => 'primary',
    'size' => 'md',
    'disabled' => false,
    'icon' => null,
    'iconPosition' => 'left'
])

@php
    $baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800';

    $variantClasses = [
        'primary' => 'bg-indigo-600 hover:bg-indigo-700 text-white focus:ring-indigo-500 shadow-lg shadow-indigo-600/20',
        'secondary' => 'bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500',
        'success' => 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500',
        'danger' => 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',
        'warning' => 'bg-yellow-500 hover:bg-yellow-600 text-white focus:ring-yellow-500',
        'info' => 'bg-blue-500 hover:bg-blue-600 text-white focus:ring-blue-500',
        'light' => 'bg-gray-200 hover:bg-gray-300 text-gray-800 focus:ring-gray-300',
        'dark' => 'bg-gray-800 hover:bg-gray-900 text-white focus:ring-gray-700',
        'outline' => 'bg-transparent border border-indigo-500 text-indigo-500 hover:bg-indigo-500 hover:text-white focus:ring-indigo-500',
        'ghost' => 'bg-transparent text-indigo-500 hover:bg-indigo-100 hover:text-indigo-600 focus:ring-indigo-500',
    ];

    $sizeClasses = [
        'xs' => 'px-2.5 py-1.5 text-xs',
        'sm' => 'px-3 py-2 text-sm',
        'md' => 'px-4 py-2 text-sm',
        'lg' => 'px-5 py-2.5 text-base',
        'xl' => 'px-6 py-3 text-lg',
    ];

    $disabledClasses = 'opacity-50 cursor-not-allowed';

    $classes = $baseClasses . ' ' . $variantClasses[$variant] . ' ' . $sizeClasses[$size] . ' ' . ($disabled ? $disabledClasses : '');
@endphp

<button
    {{ $attributes->merge(['type' => $type, 'class' => $classes, 'disabled' => $disabled]) }}
>
    @if ($icon && $iconPosition === 'left')
        <span class="mr-2">
            {{ $icon }}
        </span>
    @endif

    {{ $slot }}

    @if ($icon && $iconPosition === 'right')
        <span class="ml-2">
            {{ $icon }}
        </span>
    @endif
</button>
