<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use App\Models\ReplyComment;
use App\Notifications\ReplyCommentNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class ReplyCommentController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Store a newly created reply.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $commentId
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $commentId)
    {
        $request->validate([
            'content' => 'required|string'
        ]);

        $comment = Comment::findOrFail($commentId);
        $post = $comment->post;
        $currentUser = Auth::user();

        $reply = new ReplyComment([
            'content' => $request->content,
            'user_id' => Auth::id(),
            'comment_id' => $comment->id
        ]);

        $reply->save();

        if ($comment->user_id !== $currentUser->id) {
            $commentOwner = $comment->user;
            $commentOwner->notify(new ReplyCommentNotification(
                $post->id,
                $post->title,
                $comment->id,
                $reply->id,
                Str::limit($reply->content, 50),
                $currentUser->name
            ));
        }

        return back()->with('success', 'Balasan berhasil ditambahkan');
    }

    /**
     * Delete a reply.
     *
     * @param  \App\Models\ReplyComment  $reply
     * @return \Illuminate\Http\Response
     */
    public function destroy(ReplyComment $reply)
    {
        // Check if user is allowed to delete this reply
        if (Auth::id() !== $reply->user_id && Auth::user()->role !== 'admin') {
            return back()->with('error', 'Anda tidak memiliki izin untuk menghapus balasan ini');
        }

        $reply->delete();

        return back()->with('success', 'Balasan berhasil dihapus');
    }
}
