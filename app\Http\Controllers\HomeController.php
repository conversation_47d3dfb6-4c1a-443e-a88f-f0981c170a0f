<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\Tag;
use App\Models\Comment;
use App\Models\OrganizationStructure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class HomeController extends Controller
{
    public function index()
    {
        // Featured Posts
        $featuredPosts = Post::with(['user', 'category', 'tags'])
            ->orderBy('views', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($post) {
                return [
                    'title' => strval($post->title),
                    'slug' => strval($post->slug),
                    'img' => $post->image
                        ? asset('storage/' . $post->image)
                        : asset('home/assets/images/featured-1.png'),
                    'alt' => strval($post->title),
                    'author' => $post->user ? strval($post->user->name) : 'Anonymous',
                    'author_img' => $post->user && $post->user->profile && $post->user->profile->avatar
                        ? asset('storage/' . $post->user->profile->avatar)
                        : asset('home/assets/images/author-1.png'),
                    'date' => $post->created_at->format('d M Y'),
                    'views' => intval($post->views),
                    'likes' => intval($post->likes),
                    'dislikes' => intval($post->dislikes),
                    'category' => $post->category ? strval($post->category->title) : 'Umum',
                    'tags' => $post->tags->map(function ($tag) {
                        return [
                            'title' => '#' . strval($tag->title),
                            'slug' => strval($tag->slug)
                        ];
                    })->toArray()
                ];
            })->toArray();

        // Popular Posts
        $popularPosts = Post::with(['user.profile'])
            ->orderBy('views', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($post) {
                return [
                    'img' => $post->image
                        ? asset('storage/' . $post->image)
                        : asset('home/assets/images/popular-post-1.jpg'),
                    'alt' => strval($post->title ?? 'Popular Post'),
                    'title' => strval($post->title ?? ''),
                    'read_time' => strval(max(1, ceil(strlen(strip_tags($post->content ?? '')) / 1000))) . ' mins read',
                    'datetime' => $post->created_at ? $post->created_at->format('Y-m-d') : date('Y-m-d'),
                    'date' => $post->created_at ? $post->created_at->format('d F Y') : date('d F Y'),
                    'views' => intval($post->views ?? 0),
                    'slug' => strval($post->slug ?? '')
                ];
            })->toArray();

        // Recent Posts
        $recentPosts = Post::with(['user.profile', 'category', 'tags', 'comments'])
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($post) {
                return [
                    'img' => $post->image
                        ? asset('storage/' . $post->image)
                        : asset('home/assets/images/recent-post-1.jpg'),
                    'alt' => strval($post->title ?? 'Recent Post'),
                    'badge' => strval($post->category ? $post->category->title : 'Umum'),
                    'title' => strval($post->title ?? ''),
                    'desc' => strval($post->description ?? ''),
                    'tags' => $post->tags->map(function ($tag) {
                        return [
                            'title' => '#' . strval($tag->title ?? ''),
                            'slug' => strval($tag->slug ?? '')
                        ];
                    })->toArray(),
                    'slug' => strval($post->slug ?? ''),
                    'views' => intval($post->views ?? 0),
                    'likes' => intval($post->likes ?? 0),
                    'dislikes' => intval($post->dislikes ?? 0),
                    'comments_count' => $post->comments->count(),
                    'total_reactions' => intval($post->likes ?? 0) + intval($post->dislikes ?? 0) + $post->comments->count()
                ];
            })->toArray();

        // Latest Comments
        $latestComments = Comment::with(['user.profile', 'post'])
            ->latest()
            ->limit(3)
            ->get()
            ->map(function ($comment) {
                return [
                    'text' => '"' . strval(strlen($comment->content ?? '') > 100
                        ? substr($comment->content, 0, 100) . '...'
                        : ($comment->content ?? '')) . '"',
                    'img' => $comment->user && $comment->user->profile && $comment->user->profile->avatar
                        ? asset('storage/' . $comment->user->profile->avatar)
                        : asset('home/assets/images/author-' . rand(1, 8) . '.png'),
                    'author' => strval($comment->user ? $comment->user->name : 'Anonymous'),
                    'datetime' => $comment->created_at ? $comment->created_at->format('Y-m-d') : date('Y-m-d'),
                    'date' => $comment->created_at ? $comment->created_at->format('d F Y') : date('d F Y'),
                    'post_slug' => strval($comment->post ? $comment->post->slug : ''),
                    'post_id' => strval($comment->post ? $comment->post->id : '')
                ];
            })->toArray();

        // Popular Tags
        $popularTags = Tag::withCount('posts')
            ->orderBy('posts_count', 'desc')
            ->limit(12)
            ->get()
            ->map(function ($tag) {
                return [
                    'id' => intval($tag->id),
                    'slug' => strval($tag->slug ?? ''),
                    'title' => strval($tag->title ?? ''),
                    'img' => $tag->image
                        ? asset('storage/' . $tag->image)
                        : asset('home/assets/images/tag' . rand(1, 12) . '.png'),
                    'alt' => strval($tag->title ?? 'Tag Image'),
                    'label' => strval($tag->title ?? ''),
                    'posts_count' => intval($tag->posts_count ?? 0)
                ];
            })->toArray();

        $bidangSliderItems = OrganizationStructure::where('is_active', true)
            ->orderBy('order')
            ->limit(9)
            ->get()
            ->map(function ($structure) {
                return [
                    'id' => $structure->id,
                    'img' => $structure->image
                        ? asset('storage/' . $structure->image)
                        : asset('home/assets/images/topic-' . rand(1, 5) . '.png'),
                    'title' => $structure->position_name,
                    'subtitle' => $structure->name,
                    'alt' => $structure->position_name,
                    'description' => $structure->description
                ];
            })->toArray();

        return view('home', compact(
            'featuredPosts',
            'popularPosts',
            'recentPosts',
            'latestComments',
            'popularTags',
            'bidangSliderItems'
        ));
    }
}



