<svg width="824" height="811" viewBox="0 0 824 811" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.3" filter="url(#filter0_f_42_1701)">
<ellipse cx="412" cy="405.5" rx="162" ry="155.5" fill="url(#paint0_linear_42_1701)"/>
</g>
<defs>
<filter id="filter0_f_42_1701" x="0" y="0" width="824" height="811" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="125" result="effect1_foregroundBlur_42_1701"/>
</filter>
<linearGradient id="paint0_linear_42_1701" x1="269.154" y1="561" x2="612.754" y2="511.908" gradientUnits="userSpaceOnUse">
<stop stop-color="#0EA5EA"/>
<stop offset="1" stop-color="#0BD1D1"/>
</linearGradient>
</defs>
</svg>
