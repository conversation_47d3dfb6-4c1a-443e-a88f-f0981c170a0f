<!--
    Created by: <PERSON><PERSON><PERSON><PERSON> Karaman
    Portfolio: https://irhamkaraman.my.id
-->

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>
        @hasSection('title')
            @yield('title') - {{ env('APP_NAME') }}@else{{ env('APP_NAME') }}
        @endif
    </title>

    {{-- Primary Meta Tags --}}
    <meta name="title" content="IMM Al-Qossam - Ikatan Mahasiswa Muhammadiyah" />
    <meta name="description"
        content="Website resmi IMM Al-Qossam - Wadah perjuangan dan pengkaderan mahasiswa Muhammadiyah" />

    {{-- Favicon --}}
    <link rel="icon" href="{{ asset('home/favicon_IMM_Al_Qossam.png') }}" type="image/x-icon">

    {{-- Goggle Fonts --}}
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@300;400;500;600;700;800&display=swap"
        rel="stylesheet" />

    {{-- Styles --}}
    <link rel="stylesheet" href="{{ asset('home/assets/css/style.css') }}">

    {{-- Preload Img --}}
    <link rel="preload" as="image" href="{{ asset('home/assets/images/hero-banner.png') }}" />
    <link rel="preload" as="image" href="{{ asset('home/assets/images/pattern-2.svg') }}" />
    <link rel="preload" as="image" href="{{ asset('home/assets/images/pattern-3.svg') }}" />

    {{-- Custom Styles --}}
    <style>
        /* Global styles for avatar/profile images */
        .profile-banner {
            border-radius: 50% !important;
            object-fit: cover !important;
            aspect-ratio: 1/1 !important;
            overflow: hidden !important;
        }
    </style>
</head>

<body>
    <x-navbar.Navbar :user="auth()->user()" />
    @yield('content')
    @include('components.footer.Footer')

    <script src="{{ asset('home/assets/js/script.js') }}"></script>
    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
</body>

</html>
