<div class="flex items-center justify-between mb-6">
    <h1 class="text-2xl font-bold text-indigo-100">{{ $title ?? 'Notifikasi' }}</h1>
    @if (isset($showRefresh) && $showRefresh)
        <a href="{{ route('notifications.index') }}"
            class="text-indigo-300 hover:text-indigo-100 text-sm flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
        </a>
    @endif
</div>

@if (isset($showTabs) && $showTabs)
    <!-- Tab navigation -->
    <div class="mb-6 border-b border-gray-700">
        <div class="flex space-x-8">
            <button id="all-tab" class="px-4 py-2 border-b-2 border-indigo-500 text-indigo-100 font-medium">
                Semua <span
                    class="ml-1 inline-flex items-center justify-center w-5 h-5 text-xs rounded-full bg-indigo-600">{{ $allCount }}</span>
            </button>
            <button id="unread-tab" class="px-4 py-2 text-gray-400 hover:text-indigo-100">
                Belum Dibaca <span
                    class="ml-1 inline-flex items-center justify-center w-5 h-5 text-xs rounded-full bg-indigo-600 animated-pulse">{{ $unreadCount }}</span>
            </button>
            <button id="read-tab" class="px-4 py-2 text-gray-400 hover:text-indigo-100">
                Sudah Dibaca <span
                    class="ml-1 inline-flex items-center justify-center w-5 h-5 text-xs rounded-full bg-gray-600">{{ $readCount }}</span>
            </button>
        </div>
    </div>
@endif

@if (session('error'))
    <div class="mb-6 bg-red-900/60 text-red-200 p-4 rounded-lg border border-red-800 flex items-start">
        <div class="mr-3 text-red-400">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clip-rule="evenodd" />
            </svg>
        </div>
        <div>
            <p class="font-medium">{{ session('error') }}</p>
            <p class="text-xs mt-1">ID yang dituju mungkin tidak valid atau bukan UUID yang benar.</p>
        </div>
    </div>
@endif
