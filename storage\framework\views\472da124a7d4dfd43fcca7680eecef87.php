<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['name', 'label', 'options' => [], 'selected' => null, 'grid' => true]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['name', 'label', 'options' => [], 'selected' => null, 'grid' => true]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="mb-6">
    <label class="block text-sm font-medium text-indigo-100 mb-3"><?php echo e($label); ?></label>
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
        <?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <label class="block">
                <input
                    type="radio"
                    name="<?php echo e($name); ?>"
                    value="<?php echo e($option['id'] ?? $option['value']); ?>"
                    <?php if($option['id'] == old($name, $selected)): echo 'checked'; endif; ?>
                    class="hidden peer"
                >
                <div class="p-3 bg-gray-800 rounded-lg border border-gray-700 hover:border-indigo-400 peer-checked:border-indigo-500 peer-checked:bg-indigo-900/30 transition duration-200">
                    <?php if(isset($option['image'])): ?>
                        <img src="<?php echo e(asset('storage/'.$option['image'])); ?>" alt="<?php echo e($option['name']); ?>" class="w-full h-20 object-cover rounded mb-2">
                    <?php endif; ?>
                    <span class="text-indigo-100 font-medium"><?php echo e($option['name'] ?? $option['label']); ?></span>
                    <?php if(isset($option['description'])): ?>
                        <span class="text-gray-400 text-xs mt-1 block"><?php echo e($option['description']); ?></span>
                    <?php endif; ?>
                </div>
            </label>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
    <?php $__errorArgs = [$name];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
        <p class="mt-1 text-sm text-red-500"><?php echo e($message); ?></p>
    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/components/form/radio-group.blade.php ENDPATH**/ ?>