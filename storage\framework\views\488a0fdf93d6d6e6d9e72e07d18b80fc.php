<div class="space-y-8 p-4">
    <h2 class="text-xl font-bold text-indigo-200 border-b border-gray-700 pb-2">Panel Pengguna</h2>

    <!-- Panel Statistik Personal -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Recent Activity Card -->
        <div
            class="md:col-span-2 p-6 bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl shadow-xl border border-gray-700">
            <h3 class="font-semibold text-indigo-300 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                Aktivitas Terakhir
            </h3>
            <div class="space-y-4">
                <?php $__empty_1 = true; $__currentLoopData = $userPanelData['recent_activities'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="flex items-start">
                        <div
                            class="flex-shrink-0 h-10 w-10 rounded-full <?php echo e($activity['type'] == 'post' ? 'bg-indigo-500/20 text-indigo-400' : 'bg-purple-500/20 text-purple-400'); ?> flex items-center justify-center">
                            <?php if($activity['type'] == 'post'): ?>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                    </path>
                                </svg>
                            <?php else: ?>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z">
                                    </path>
                                </svg>
                            <?php endif; ?>
                        </div>
                        <div class="ml-3">
                            <?php if($activity['type'] == 'post'): ?>
                                <p class="text-indigo-100">Anda membuat postingan "<?php echo e($activity['title']); ?>"</p>
                            <?php else: ?>
                                <p class="text-indigo-100">Anda mengomentari postingan "<?php echo e($activity['post_title']); ?>"
                                </p>
                            <?php endif; ?>
                            <p class="text-xs text-gray-400"><?php echo e($activity['time']->diffForHumans()); ?></p>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="text-center py-4">
                        <p class="text-gray-400">Belum ada aktivitas terbaru</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Achievement Card -->
        <div
            class="p-6 bg-gradient-to-br from-indigo-900/30 to-purple-900/30 rounded-xl shadow-xl border border-indigo-800/50">
            <h3 class="font-semibold text-indigo-300 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z">
                    </path>
                </svg>
                Pencapaian
            </h3>
            <div class="space-y-3">
                <?php $__currentLoopData = $userPanelData['achievements'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $achievement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center">
                        <div
                            class="w-8 h-8 rounded-full <?php echo e($achievement['completed'] ? 'bg-yellow-400 text-gray-900' : 'bg-gray-700 text-gray-400'); ?> flex items-center justify-center font-bold text-xs mr-3">
                            <?php echo e($index + 1); ?></div>
                        <div>
                            <p class="text-sm <?php echo e($achievement['completed'] ? 'text-indigo-100' : 'text-gray-400'); ?>">
                                <?php echo e($achievement['name']); ?></p>
                            <div class="mt-1 h-1.5 w-full bg-gray-700 rounded-full overflow-hidden">
                                <div class="h-full <?php echo e($achievement['completed'] ? 'bg-gradient-to-r from-yellow-400 to-yellow-300' : 'bg-gradient-to-r from-gray-600 to-gray-500'); ?> rounded-full"
                                    style="width: <?php echo e($achievement['progress']); ?>%"></div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- Quick Action Buttons -->
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        <a href="<?php echo e(route('posts.create')); ?>"
            class="p-5 bg-indigo-600/20 border border-indigo-600/40 rounded-xl flex flex-col items-center justify-center text-center hover:bg-indigo-600/30 transition duration-300 group">
            <div
                class="w-12 h-12 rounded-full bg-indigo-600/30 flex items-center justify-center mb-3 group-hover:bg-indigo-600/50 transition duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-400" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
            </div>
            <span class="text-indigo-200 font-medium">Buat Postingan</span>
        </a>
        <a href="<?php echo e(route('posts.index')); ?>"
            class="p-5 bg-purple-600/20 border border-purple-600/40 rounded-xl flex flex-col items-center justify-center text-center hover:bg-purple-600/30 transition duration-300 group">
            <div
                class="w-12 h-12 rounded-full bg-purple-600/30 flex items-center justify-center mb-3 group-hover:bg-purple-600/50 transition duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-400" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
                    </path>
                </svg>
            </div>
            <span class="text-purple-200 font-medium">Postingan Saya</span>
        </a>
        <a href="<?php echo e(route('profile.index')); ?>"
            class="p-5 bg-pink-600/20 border border-pink-600/40 rounded-xl flex flex-col items-center justify-center text-center hover:bg-pink-600/30 transition duration-300 group">
            <div
                class="w-12 h-12 rounded-full bg-pink-600/30 flex items-center justify-center mb-3 group-hover:bg-pink-600/50 transition duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-pink-400" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <span class="text-pink-200 font-medium">Edit Profil</span>
        </a>
        <a href="<?php echo e(route('notifications.index')); ?>"
            class="p-5 bg-blue-600/20 border border-blue-600/40 rounded-xl flex flex-col items-center justify-center text-center hover:bg-blue-600/30 transition duration-300 group">
            <div
                class="w-12 h-12 rounded-full bg-blue-600/30 flex items-center justify-center mb-3 group-hover:bg-blue-600/50 transition duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9">
                    </path>
                </svg>
            </div>
            <span class="text-blue-200 font-medium">Notifikasi</span>
        </a>
    </div>

    <!-- Progress Chart -->
    
</div>

<!-- Tambahkan Chart Data untuk User -->
<?php $__env->startPush('scripts'); ?>
    <script>
        function setupUserActivityChart() {
            const userActivityCtx = document.getElementById('userActivityChart')?.getContext('2d');
            if (userActivityCtx) {
                new Chart(userActivityCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'],
                        datasets: [{
                            label: 'Postingan',
                            data: [1, 0, 2, 1, 3, 0, 1], // Data statis sebagai contoh
                            backgroundColor: 'rgba(129, 140, 248, 0.6)',
                            borderColor: '#818cf8',
                            borderWidth: 1
                        }, {
                            label: 'Komentar',
                            data: [3, 5, 2, 4, 1, 2, 3], // Data statis sebagai contoh
                            backgroundColor: 'rgba(244, 114, 182, 0.6)',
                            borderColor: '#f472b6',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(107, 114, 128, 0.3)',
                                    borderColor: 'rgba(107, 114, 128, 0.3)'
                                },
                                ticks: {
                                    color: '#d1d5db',
                                    precision: 0
                                }
                            },
                            x: {
                                grid: {
                                    display: false,
                                    borderColor: 'rgba(107, 114, 128, 0.3)'
                                },
                                ticks: {
                                    color: '#d1d5db'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    color: '#d1d5db',
                                    boxWidth: 12,
                                    padding: 20
                                }
                            }
                        }
                    }
                });
            }
        }

        document.addEventListener('DOMContentLoaded', setupUserActivityChart);
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/components/dashboard/user/panel.blade.php ENDPATH**/ ?>