@props(['post'])

<div
    class="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700 hover:border-indigo-500 transition-all duration-300 hover:shadow-xl hover:shadow-indigo-900/10 hover:-translate-y-1">
    @if ($post->image)
        <div class="h-48 overflow-hidden">
            <img src="{{ Storage::url($post->image) }}" alt="{{ $post->title }}"
                class="w-full h-full object-cover transition-transform duration-500 transform hover:scale-110">
        </div>
    @endif

    <div class="p-5">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
                @if ($post->category)
                    <span class="px-2 py-1 text-xs font-medium bg-indigo-900/70 text-indigo-300 rounded-md">
                        {{ $post->category->title }}
                    </span>
                @endif

                <span class="text-xs text-gray-400">{{ $post->created_at->diffForHumans() }}</span>
            </div>

            <div class="flex items-center space-x-1 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                </svg>
                <span class="text-xs">{{ $post->comments->count() }}</span>
            </div>
        </div>

        <h3 class="text-xl font-bold text-white mb-2 line-clamp-2">{{ $post->title }}</h3>

        <p class="text-gray-400 text-sm mb-4 line-clamp-3">{{ $post->description }}</p>

        @if ($post->tags && $post->tags->count() > 0)
            <div class="mb-4 flex flex-wrap gap-1">
                @foreach ($post->tags as $tag)
                    <span class="inline-block px-2 py-0.5 text-xs bg-gray-700 text-gray-300 rounded">
                        #{{ $tag->title }}
                    </span>
                @endforeach
            </div>
        @endif

        <div class="flex items-center justify-between pt-3 border-t border-gray-700">
            <div class="flex items-center space-x-2">
                <div class="h-8 w-8 rounded-full overflow-hidden flex items-center justify-center">
                    @if ($post->user && $post->user->avatar)
                        <img src="{{ Storage::url($post->user->avatar) }}" alt="{{ $post->user->name }}"
                            class="w-full h-full object-cover">
                    @else
                        <div class="h-full w-full bg-indigo-800 flex items-center justify-center">
                            <span
                                class="text-white text-xs font-medium">{{ strtoupper(substr($post->user->name ?? 'U', 0, 1)) }}</span>
                        </div>
                    @endif
                </div>
                <span class="text-sm font-medium text-indigo-300">{{ $post->user->name ?? 'Anonymous' }}</span>
            </div>

            {{ $slot }}
        </div>
    </div>
</div>
