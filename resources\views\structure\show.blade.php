@extends('layouts.homeLayout')

@section('title', $member->name . ' - Struktur Organisasi')

@section('content')
    <style>
        .member-detail {
            padding-top: 90px;
        }

        .member-header {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
            margin-bottom: 50px;
        }

        @media (min-width: 768px) {
            .member-header {
                grid-template-columns: 300px 1fr;
                align-items: start;
            }
        }

        .member-banner {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .member-social {
            position: absolute;
            bottom: 20px;
            left: 20px;
            display: flex;
            gap: 10px;
        }

        .social-link {
            width: 40px;
            height: 40px;
            background-color: var(--bg-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            color: var(--foreground-primary);
            font-size: 1.5rem;
            transition: 0.25s ease;
        }

        .social-link:hover {
            background-color: var(--accent);
            color: var(--white);
        }

        .member-info .hero-subtitle {
            margin-bottom: 10px;
            color: var(--accent);
            font-weight: 500;
        }

        .member-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 15px;
            margin-bottom: 20px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: var(--foreground-secondary);
        }

        .meta-item ion-icon {
            color: var(--accent);
        }

        .member-bio {
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .contact-area {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .member-detail-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
        }

        @media (min-width: 992px) {
            .member-detail-content {
                grid-template-columns: 2fr 1fr;
            }
        }

        .content-main {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .content-aside {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .detail-card {
            padding: 30px;
        }

        .about-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
            margin-top: 20px;
        }

        .about-section h3 {
            color: var(--foreground-primary);
            margin-bottom: 15px;
            position: relative;
            padding-left: 20px;
        }

        .about-section h3::before {
            content: "";
            position: absolute;
            left: 0;
            top: 8px;
            width: 10px;
            height: 10px;
            background-color: var(--accent);
            border-radius: 50%;
        }

        .program-list,
        .achievement-list {
            list-style: disc;
            padding-left: 20px;
            margin-left: 10px;
            line-height: 1.8;
        }

        .vision-mission p {
            margin-bottom: 15px;
            line-height: 1.8;
        }

        .vision-mission ul {
            list-style: disc;
            padding-left: 20px;
            margin-left: 10px;
            line-height: 1.8;
        }

        .member-quote {
            padding: 20px;
            background-color: var(--bg-secondary);
            border-left: 5px solid var(--accent);
            font-style: italic;
            line-height: 1.8;
        }

        .related-posts {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        @media (min-width: 768px) {
            .related-posts {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .related-post-card {
            overflow: hidden;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .related-post-card .card-content {
            padding: 15px;
        }

        .department-members {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 20px;
        }

        .member-card {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .member-avatar {
            border-radius: 50%;
            overflow: hidden;
        }

        .member-name {
            color: var(--foreground-primary);
            transition: 0.25s ease;
        }

        .member-name:hover {
            color: var(--accent);
        }

        .member-position {
            color: var(--foreground-secondary);
            font-size: 0.9rem;
        }

        .cta-card {
            background-color: var(--bg-secondary);
            text-align: center;
            padding: 40px 20px;
        }

        .cta-card .card-title {
            margin-bottom: 20px;
        }

        .cta-card .card-text {
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .no-members-msg {
            color: var(--foreground-secondary);
            font-style: italic;
            text-align: center;
            padding: 20px 0;
        }

        .other-members {
            margin-top: 30px;
            position: relative;
        }

        .other-members .slider-card {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            background-color: var(--bg-primary);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: 0.3s ease;
        }

        .other-members .slider-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .other-members .slider-banner {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 15px;
            overflow: hidden;
            border: 3px solid var(--accent);
        }

        .other-members .slider-content {
            margin-top: 15px;
        }

        .other-members .slider-title {
            color: var(--foreground-primary);
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 5px;
            display: block;
        }

        .other-members .slider-subtitle {
            color: var(--foreground-secondary);
            font-size: 0.9rem;
        }

        .other-members .btn-group {
            position: absolute;
            top: -80px;
            right: 0;
            display: flex;
            gap: 10px;
        }

        @media (max-width: 768px) {
            .other-members .btn-group {
                top: -60px;
            }
        }
    </style>
    <section class="section member-detail" aria-labelledby="member-detail-title">
        <div class="container">
            <div class="member-header">
                <div class="member-banner">
                    <img src="{{ asset($member->image) }}" width="300" height="300" alt="{{ $member->name }}"
                        class="profile-banner img-cover" />
                    <div class="member-social">
                        @if ($member->instagram)
                            <a href="https://instagram.com/{{ $member->instagram }}" class="social-link" target="_blank">
                                <ion-icon name="logo-instagram" aria-hidden="true"></ion-icon>
                            </a>
                        @endif

                        @if ($member->twitter)
                            <a href="https://twitter.com/{{ $member->twitter }}" class="social-link" target="_blank">
                                <ion-icon name="logo-twitter" aria-hidden="true"></ion-icon>
                            </a>
                        @endif

                        @if ($member->linkedin)
                            <a href="{{ $member->linkedin }}" class="social-link" target="_blank">
                                <ion-icon name="logo-linkedin" aria-hidden="true"></ion-icon>
                            </a>
                        @endif
                    </div>
                </div>

                <div class="member-info">
                    <p class="hero-subtitle">{{ $member->position_name }}</p>
                    <h1 class="headline headline-1 section-title" id="member-detail-title">
                        {{ $member->name }}
                    </h1>
                    <div class="member-meta">
                        <div class="meta-item">
                            <ion-icon name="bookmark-outline" aria-hidden="true"></ion-icon>
                            <span>Bidang {{ $member->position_name }}</span>
                        </div>

                        <div class="meta-item">
                            <ion-icon name="school-outline" aria-hidden="true"></ion-icon>
                            <span>{{ $member->study_program ?? 'Program Studi FAI' }}</span>
                        </div>

                        <div class="meta-item">
                            <ion-icon name="calendar-outline" aria-hidden="true"></ion-icon>
                            <span>Periode {{ $member->period ?? '2023-2024' }}</span>
                        </div>
                    </div>

                    <p class="member-bio">
                        {{ $member->bio ?? 'Pengurus aktif PK IMM Al-Qossam yang berkomitmen menjalankan amanah dan kewajiban dakwah di kampus. Melalui peran strategis di organisasi, berupaya menjadi agen perubahan dan penggerak kebijakan yang bermanfaat untuk mahasiswa dan masyarakat luas.' }}
                    </p>

                    <div class="contact-area">
                        @if ($member->email)
                            <a href="mailto:{{ $member->email }}" class="btn btn-primary">
                                <span class="span">Kirim Email</span>
                                <ion-icon name="mail-outline" aria-hidden="true"></ion-icon>
                            </a>
                        @endif

                        @if ($member->phone)
                            <a href="https://wa.me/{{ str_replace('+', '', $member->phone) }}" class="btn btn-secondary">
                                <span class="span">WhatsApp</span>
                                <ion-icon name="logo-whatsapp" aria-hidden="true"></ion-icon>
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            <div class="member-detail-content">
                <div class="content-main">
                    <div class="card detail-card">
                        <h2 class="headline headline-2">Tentang {{ $member->name }}</h2>

                        <div class="about-content">
                            <div class="about-section">
                                <h3 class="headline headline-3">Program Kerja</h3>
                                <ul class="program-list">
                                    @if (isset($member->programs) && is_array($member->programs))
                                        @foreach ($member->programs as $program)
                                            <li>{{ $program }}</li>
                                        @endforeach
                                    @else
                                        <li>Menyelenggarakan kajian rutin bidang {{ $member->department }}</li>
                                        <li>Membentuk tim untuk proyek {{ $member->department }}</li>
                                        <li>Pengembangan kapasitas anggota bidang</li>
                                        <li>Koordinasi dengan lembaga-lembaga terkait</li>
                                    @endif
                                </ul>
                            </div>

                            <div class="about-section">
                                <h3 class="headline headline-3">Prestasi & Kontribusi</h3>
                                <ul class="achievement-list">
                                    @if (isset($member->achievements) && is_array($member->achievements))
                                        @foreach ($member->achievements as $achievement)
                                            <li>{{ $achievement }}</li>
                                        @endforeach
                                    @else
                                        <li>Aktif dalam kegiatan IMM Al-Qossam</li>
                                        <li>Berkontribusi dalam pengembangan organisasi</li>
                                        <li>Membangun jaringan dengan organisasi eksternal</li>
                                    @endif
                                </ul>
                            </div>

                            <div class="about-section">
                                <h3 class="headline headline-3">Visi & Misi</h3>
                                <div class="vision-mission">
                                    <p>
                                        <strong>Visi:</strong>
                                        {{ $member->vision ?? 'Membangun bidang ' . $member->department . ' yang progresif, inovatif, dan responsif terhadap kebutuhan mahasiswa dan masyarakat.' }}
                                    </p>
                                    <p>
                                        <strong>Misi:</strong>
                                    </p>
                                    <ul>
                                        @if (isset($member->missions) && is_array($member->missions))
                                            @foreach ($member->missions as $mission)
                                                <li>{{ $mission }}</li>
                                            @endforeach
                                        @else
                                            <li>Mengoptimalkan fungsi bidang {{ $member->department }} melalui
                                                program-program yang terukur</li>
                                            <li>Membangun kaderisasi yang berkelanjutan</li>
                                            <li>Memperluas jaringan kerjasama dengan berbagai pihak</li>
                                            <li>Meningkatkan kualitas sumber daya kader melalui pelatihan berkelanjutan</li>
                                        @endif
                                    </ul>
                                </div>
                            </div>

                            <div class="about-section">
                                <h3 class="headline headline-3">Motto</h3>
                                <blockquote class="member-quote">
                                    "{{ $member->motto ?? 'Fastabiqul Khairat - Berlomba-lomba dalam kebaikan' }}"
                                </blockquote>
                            </div>
                        </div>
                    </div>

                    @if (count($relatedPosts) > 0)
                        <div class="card detail-card">
                            <h2 class="headline headline-2">Tulisan Terkait</h2>

                            <div class="related-posts">
                                @foreach ($relatedPosts as $post)
                                    <article class="related-post-card">
                                        <figure class="card-banner img-holder" style="--width: 271; --height: 258">
                                            <img src="{{ asset($post->thumbnail) }}" width="271" height="258"
                                                loading="lazy" alt="{{ $post->title }}" class="img-cover" />
                                        </figure>

                                        <div class="card-content">
                                            <a href="{{ route('posts.show', $post->slug) }}" class="card-btn"
                                                aria-label="{{ $post->title }}">
                                                <ion-icon name="arrow-forward-outline" aria-hidden="true"></ion-icon>
                                            </a>

                                            <a href="{{ route('categories.show', $post->category->slug) }}"
                                                class="card-badge">{{ $post->category->name }}</a>

                                            <h3 class="headline headline-3">
                                                <a href="{{ route('posts.show', $post->slug) }}" class="card-title">
                                                    {{ $post->title }}
                                                </a>
                                            </h3>

                                            <div class="card-meta">
                                                <div class="publish-date">
                                                    <ion-icon name="calendar-outline" aria-hidden="true"></ion-icon>
                                                    <time datetime="{{ $post->created_at->format('Y-m-d') }}">
                                                        {{ $post->created_at->format('d F Y') }}
                                                    </time>
                                                </div>
                                            </div>
                                        </div>
                                    </article>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>

                <div class="content-aside">
                    <div class="card aside-card">
                        <h3 class="headline headline-2 aside-title">
                            <span class="span">Anggota Bidang {{ $member->position_name }}</span>
                        </h3>

                        <div class="department-members">
                            @foreach ($departmentMembers as $deptMember)
                                <div class="member-card">
                                    <figure class="member-avatar img-holder" style="--width: 60; --height: 60">
                                        <img src="{{ asset($deptMember->image) }}" width="60" height="60"
                                            loading="lazy" alt="{{ $deptMember->name }}"
                                            class="profile-banner img-cover" />
                                    </figure>

                                    <h4 class="headline headline-4">
                                        <a href="{{ route('organization.show', $deptMember->id) }}"
                                            class="member-name">{{ $deptMember->name }}</a>
                                    </h4>
                                    <p class="member-position">{{ $deptMember->position_name }}</p>
                                </div>
                            @endforeach

                            @if (count($departmentMembers) == 0)
                                <p class="no-members-msg">Belum ada anggota lain di bidang ini.</p>
                            @endif
                        </div>
                    </div>

                    <div class="card aside-card cta-card">
                        <h3 class="headline headline-2 card-title">Bergabung dengan IMM Al-Qossam</h3>

                        <p class="card-text">
                            Jadilah bagian dari gerakan mahasiswa Islam yang memperjuangkan nilai-nilai kepemimpinan,
                            keilmuan, dan dakwah di kampus.
                        </p>

                        <a href="{{ route('login') }}" class="btn btn-primary">
                            <span class="span">Daftar Sekarang</span>
                            <ion-icon name="arrow-forward-outline" aria-hidden="true"></ion-icon>
                        </a>
                    </div>

                    <div class="card aside-card insta-card">
                        <a href="#" class="logo">
                            <x-logo.logo width="119" height="37" />
                        </a>

                        <p class="card-text">Ikuti Instagram Kami</p>

                        @php
                            $instaItems = [
                                ['img' => 'home/assets/images/insta-post-1.png', 'alt' => 'insta post 1'],
                                ['img' => 'home/assets/images/insta-post-2.png', 'alt' => 'insta post 2'],
                                ['img' => 'home/assets/images/insta-post-3.png', 'alt' => 'insta post 3'],
                                ['img' => 'home/assets/images/insta-post-4.png', 'alt' => 'insta post 4'],
                                ['img' => 'home/assets/images/insta-post-5.png', 'alt' => 'insta post 5'],
                                ['img' => 'home/assets/images/insta-post-6.png', 'alt' => 'insta post 6'],
                            ];
                        @endphp
                        <x-insta.InstaList :items="$instaItems" />
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="section org-structure" aria-labelledby="org-structure-label">
        <div class="container">
            <h2 class="headline headline-2 section-title" id="org-structure-label">
                <span class="span">Pengurus Lainnya</span>
            </h2>

            <p class="section-text">Kenali pengurus PK IMM Al-Qossam periode {{ $member->period ?? '2023-2024' }}</p>

            <div class="other-members slider" data-slider>
                <ul class="slider-list" data-slider-container>
                    @php
                        $otherMembers = App\Models\OrganizationStructure::where('id', '!=', $member->id)
                            ->inRandomOrder()
                            ->take(6)
                            ->get();
                    @endphp

                    @foreach ($otherMembers as $otherMember)
                        <li class="slider-item">
                            <a href="{{ route('organization.show', $otherMember->id) }}" class="slider-card">
                                <figure class="slider-banner img-holder" style="--width: 170; --height: 170">
                                    <img src="{{ asset($otherMember->image) }}" width="170" height="170"
                                        loading="lazy" alt="{{ $otherMember->name }}"
                                        class="profile-banner img-cover" />
                                </figure>
                                <div class="slider-content">
                                    <span class="slider-title">{{ $otherMember->name }}</span>
                                    <p class="slider-subtitle">{{ $otherMember->position_name }}</p>
                                </div>
                            </a>
                        </li>
                    @endforeach
                </ul>

                <div class="btn-group">
                    <button class="btn-icon" aria-label="previous" data-slider-prev>
                        <ion-icon name="arrow-back" aria-hidden="true"></ion-icon>
                    </button>

                    <button class="btn-icon" aria-label="next" data-slider-next>
                        <ion-icon name="arrow-forward" aria-hidden="true"></ion-icon>
                    </button>
                </div>
            </div>
        </div>
    </section>
@endsection
