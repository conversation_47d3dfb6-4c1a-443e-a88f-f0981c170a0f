@props(['posts'])

<ul class="popular-list">
    @foreach ($posts as $post)
        <li>
            <div class="popular-card">
                <a href="{{ route('posts.show', $post['slug']) }}">
                    <figure class="card-banner img-holder" style="--width: 64; --height: 64">
                        <img src="{{ $post['img'] }}" width="64" height="64" loading="lazy" alt="{{ $post['alt'] }}"
                            class="img-cover">
                    </figure>
                </a>

                <div class="card-content">
                    <h4 class="headline headline-4 card-title">
                        <a href="{{ route('posts.show', $post['slug']) }}" class="card-title hover-2">
                            {{ $post['title'] }}
                        </a>
                    </h4>

                    <div class="wrapper">
                        <time class="publish-date" style="font-size: 14px;" datetime="{{ $post['datetime'] }}">{{ $post['date'] }}</time>

                        <div style="display: flex; align-items: center; gap: 5px; margin-bottom: 10px;">
                            <span class="span" style="font-size: 13px;">{{ number_format($post['views']) }}</span>
                            <ion-icon name="eye-outline" aria-hidden="true"></ion-icon>
                        </div>
                    </div>
                </div>
            </div>
        </li>
    @endforeach
</ul>
