function showSuccessAlert(message, redirect = null) {
    Swal.fire({
        icon: "success",
        title: "Berhasil!",
        text: message,
        timer: 2000,
        timerProgressBar: true,
        showConfirmButton: false,
        didClose: () => {
            if (redirect) {
                window.location.href = redirect;
            }
        },
    });
}

function showErrorAlert(message) {
    Swal.fire({
        icon: "error",
        title: "Oops...",
        text: message,
        confirmButtonColor: "#6366f1",
    });
}

function showConfirmDialog(
    title,
    text,
    confirmButtonText = "Ya",
    cancelButtonText = "Batal"
) {
    return Swal.fire({
        title: title,
        text: text,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#ef4444",
        cancelButtonColor: "#6366f1",
        confirmButtonText: confirmButtonText,
        cancelButtonText: cancelButtonText,
        reverseButtons: true,
    });
}

function confirmDelete(event, id, title, itemType = "") {
    event.preventDefault();

    let message = `${itemType} "${title}" akan dihapus`;
    if (itemType === "Kategori" || itemType === "Tag") {
        message += `. ${itemType} ini akan dihapus dari semua postingan terkait.`;
    } else if (itemType === "Postingan") {
        message += " permanen.";
    } else {
        message += ".";
    }

    Swal.fire({
        title: "Apakah Anda yakin?",
        text: message,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#4F46E5",
        cancelButtonColor: "#6B7280",
        confirmButtonText: "Ya, hapus!",
        cancelButtonText: "Batal",
    }).then((result) => {
        if (result.isConfirmed) {
            document.getElementById("delete-form-" + id).submit();
        }
    });
}
