@props(['label', 'name', 'value' => '', 'type' => 'text'])

<div class="editable-field group relative">
    <label class="block text-sm font-medium text-gray-400 mb-1">{{ $label }}</label>

    <div class="relative">
        <!-- Display value (click to edit) -->
        <div
            class="display-value cursor-pointer py-2 px-3 bg-gray-700/50 border border-gray-600 rounded-md text-indigo-100 hover:bg-gray-700 transition-colors duration-200 min-h-[42px] flex items-center">
            <span class="editable-display">{{ $value ?: 'Belum diisi' }}</span>

            <button type="button"
                class="edit-button ml-auto text-gray-400 hover:text-indigo-300 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
            </button>
        </div>

        <!-- Edit field (hidden by default) -->
        <div class="edit-field hidden">
            @if ($type === 'textarea')
                <textarea name="{{ $name }}"
                    class="w-full bg-gray-800 border border-indigo-500 focus:border-indigo-400 focus:ring focus:ring-indigo-300/30 rounded-md shadow-sm text-indigo-100 py-2 px-3"
                    rows="4">{{ $value }}</textarea>
            @elseif($type === 'select')
                <select name="{{ $name }}"
                    class="w-full bg-gray-800 border border-indigo-500 focus:border-indigo-400 focus:ring focus:ring-indigo-300/30 rounded-md shadow-sm text-indigo-100 py-2 px-3">
                    <option value="">Pilih {{ $label }}</option>
                </select>
            @else
                <input type="{{ $type ?? 'text' }}" name="{{ $name }}" value="{{ $value }}"
                    class="w-full bg-gray-800 border border-indigo-500 focus:border-indigo-400 focus:ring focus:ring-indigo-300/30 rounded-md shadow-sm text-indigo-100 py-2 px-3">
            @endif

            <!-- Action buttons -->
            <div class="flex items-center space-x-2 mt-2 justify-end">
                <button type="button"
                    class="cancel-button text-xs py-1 px-3 bg-gray-700 text-gray-300 rounded hover:bg-gray-600 transition-colors">
                    Batal
                </button>
                <button type="button"
                    class="save-field-button text-xs py-1 px-3 bg-indigo-600 text-white rounded hover:bg-indigo-500 transition-colors flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Simpan
                </button>
            </div>
        </div>
    </div>
</div>

@once
    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Setup all editable fields
                document.querySelectorAll('.editable-field').forEach(field => {
                    const displayValue = field.querySelector('.display-value');
                    const editField = field.querySelector('.edit-field');
                    const editButton = field.querySelector('.edit-button');
                    const cancelButton = field.querySelector('.cancel-button');
                    const saveButton = field.querySelector('.save-field-button');
                    const input = field.querySelector('input, select, textarea');

                    // Click on display or edit button
                    [displayValue, editButton].forEach(el => {
                        el.addEventListener('click', () => {
                            displayValue.classList.add('hidden');
                            editField.classList.remove('hidden');
                            input.focus();

                            // Save initial value for cancel
                            input.dataset.initialValue = input.value;

                            // Show Save button on main form
                            document.getElementById('save-button').classList.remove('hidden');
                        });
                    });

                    // Cancel button
                    cancelButton.addEventListener('click', () => {
                        // Restore initial value
                        input.value = input.dataset.initialValue;

                        displayValue.classList.remove('hidden');
                        editField.classList.add('hidden');
                    });

                    // Save single field
                    saveButton.addEventListener('click', async () => {
                        const name = input.name;
                        const value = input.value;
                        const span = field.querySelector('.editable-display');

                        try {
                            // Update display immediately
                            span.textContent = value || 'Belum diisi';
                            displayValue.classList.remove('hidden');
                            editField.classList.add('hidden');

                            // Create FormData with just this field
                            const formData = new FormData();
                            formData.append('_token', document.querySelector('input[name="_token"]')
                                .value);
                            formData.append('_method', 'PUT');
                            formData.append(name, value);

                            // Save to server
                            const response = await fetch('{{ route('profile.update.field') }}', {
                                method: 'POST',
                                body: formData,
                                headers: {
                                    'X-Requested-With': 'XMLHttpRequest'
                                }
                            });

                            const result = await response.json();

                            if (result.success) {
                                // Show a subtle success indicator
                                const successIndicator = document.createElement('div');
                                successIndicator.className =
                                    'absolute right-0 top-0 mt-2 mr-2 text-green-400 animate-pulse';
                                successIndicator.innerHTML = `
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        `;
                                displayValue.appendChild(successIndicator);

                                // Remove after animation
                                setTimeout(() => {
                                    successIndicator.remove();
                                }, 2000);
                            }
                        } catch (error) {
                            console.error('Error updating field:', error);
                        }
                    });
                });
            });
        </script>
    @endpush
@endonce
