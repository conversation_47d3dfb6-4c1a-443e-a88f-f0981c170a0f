@extends('layouts.appLayout')

@section('title', 'Edit Postingan')

@section('content')
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <x-layout.section title="Edit Postingan">
            <form action="{{ route('posts.update', $post->id) }}" method="POST" enctype="multipart/form-data"
                class="space-y-6">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="md:col-span-2">
                        <x-form.input name="title" label="Judul Postingan" placeholder="Masukkan judul..." required
                            :error="$errors->first('title')" :value="old('title', $post->title)" />
                    </div>

                    <div class="md:col-span-2">
                        <x-form.textarea name="description" label="Deskripsi Singkat"
                            placeholder="Tuliskan deskripsi singkat postingan..." rows="3" required :error="$errors->first('description')"
                            :value="old('description', $post->description)"></x-form.textarea>
                    </div>

                    <div class="md:col-span-2">
                        <x-form.ckeditor-textarea name="content" label="Konten"
                            placeholder="Tulis konten postingan anda disini..." rows="8" required :error="$errors->first('content')"
                            :value="old('content', $post->content)" />
                    </div>

                    <div class="md:col-span-2">
                        @php
                            $categoryOptions = $categories
                                ->map(function ($category) {
                                    return [
                                        'id' => $category->id,
                                        'name' => $category->title,
                                        'description' => $category->description,
                                        'image' => $category->banner,
                                    ];
                                })
                                ->toArray();
                        @endphp

                        <x-form.radio-group name="category_id" label="Pilih Kategori" :options="$categoryOptions" :selected="old('category_id', $post->category_id)" />

                        @can('dashboard-admin')
                            <div class="text-right mt-2">
                                <a href="{{ route('categories.index') }}" class="text-xs text-indigo-400 hover:text-indigo-300">
                                    Kelola kategori
                                </a>
                            </div>
                        @endcan
                    </div>

                    <div class="md:col-span-2">
                        @php
                            $tagOptions = $tags
                                ->map(function ($tag) {
                                    return [
                                        'id' => $tag->id,
                                        'name' => $tag->title,
                                        'description' => $tag->description,
                                    ];
                                })
                                ->toArray();
                        @endphp

                        <x-form.checkbox-group name="tags" label="Pilih Tag (Maksimal 3)" :options="$tagOptions" :selected="$selectedTags" max="3" />

                        @can('dashboard-admin')
                            <div class="text-right mt-2">
                                <a href="{{ route('tags.index') }}" class="text-xs text-indigo-400 hover:text-indigo-300">
                                    Kelola tag
                                </a>
                            </div>
                        @endcan
                    </div>

                    <div class="md:col-span-2">
                        <x-form.file-upload name="image" label="Gambar Postingan" accept="image/jpeg,image/png,image/gif"
                            :error="$errors->first('image')" helper="JPG, PNG, atau GIF. Maksimal 2MB." />

                        @if ($post->image)
                            <div class="mt-2 text-center">
                                <p class="text-sm text-gray-400 mb-2">Gambar saat ini:</p>
                                <img src="{{ Storage::url($post->image) }}" alt="{{ $post->title }}"
                                    class="h-32 w-auto object-cover rounded-md mx-auto">
                            </div>
                        @endif
                    </div>
                </div>

                <div class="flex justify-end pt-6 border-t border-gray-700">
                    <x-form.button onclick="window.history.back()" type="button" variant="secondary" class="mr-2">
                        Batal
                    </x-form.button>

                    <x-form.button type="submit" variant="primary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Simpan Perubahan
                    </x-form.button>
                </div>
            </form>
        </x-layout.section>
    </div>
@endsection
