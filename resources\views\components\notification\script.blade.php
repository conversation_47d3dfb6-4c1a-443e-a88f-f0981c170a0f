<script>
    document.addEventListener('DOMContentLoaded', function() {
        const allTab = document.getElementById('all-tab');
        const unreadTab = document.getElementById('unread-tab');
        const readTab = document.getElementById('read-tab');

        const allNotifications = document.getElementById('all-notifications');
        const unreadNotifications = document.getElementById('unread-notifications');
        const readNotifications = document.getElementById('read-notifications');
        const paginationContainer = document.getElementById('pagination-container');

        allTab.addEventListener('click', function() {
            // Show all, hide others
            allNotifications.classList.remove('hidden');
            unreadNotifications.classList.add('hidden');
            readNotifications.classList.add('hidden');
            paginationContainer.classList.remove('hidden');

            // Update active tab
            allTab.classList.add('border-b-2', 'border-indigo-500', 'text-indigo-100');
            allTab.classList.remove('text-gray-400');
            unreadTab.classList.remove('border-b-2', 'border-indigo-500', 'text-indigo-100');
            unreadTab.classList.add('text-gray-400');
            readTab.classList.remove('border-b-2', 'border-indigo-500', 'text-indigo-100');
            readTab.classList.add('text-gray-400');
        });

        unreadTab.addEventListener('click', function() {
            // Show unread, hide others
            allNotifications.classList.add('hidden');
            unreadNotifications.classList.remove('hidden');
            readNotifications.classList.add('hidden');
            paginationContainer.classList.add('hidden');

            // Update active tab
            allTab.classList.remove('border-b-2', 'border-indigo-500', 'text-indigo-100');
            allTab.classList.add('text-gray-400');
            unreadTab.classList.add('border-b-2', 'border-indigo-500', 'text-indigo-100');
            unreadTab.classList.remove('text-gray-400');
            readTab.classList.remove('border-b-2', 'border-indigo-500', 'text-indigo-100');
            readTab.classList.add('text-gray-400');
        });

        readTab.addEventListener('click', function() {
            // Show read, hide others
            allNotifications.classList.add('hidden');
            unreadNotifications.classList.add('hidden');
            readNotifications.classList.remove('hidden');
            paginationContainer.classList.add('hidden');

            // Update active tab
            allTab.classList.remove('border-b-2', 'border-indigo-500', 'text-indigo-100');
            allTab.classList.add('text-gray-400');
            unreadTab.classList.remove('border-b-2', 'border-indigo-500', 'text-indigo-100');
            unreadTab.classList.add('text-gray-400');
            readTab.classList.add('border-b-2', 'border-indigo-500', 'text-indigo-100');
            readTab.classList.remove('text-gray-400');
        });
    });
</script>

<style>
    .animate-pulse {
        animation: pulse 1.5s infinite;
    }

    @keyframes pulse {

        0%,
        100% {
            opacity: 1;
        }

        50% {
            opacity: 0.5;
        }
    }
</style>
