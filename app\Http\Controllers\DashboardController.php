<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\User;
use App\Models\Comment;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $isAdmin = $user->role === 'admin';

        if ($isAdmin) {
            $currentMonthPosts = Post::whereMonth('created_at', Carbon::now()->month)
                ->whereYear('created_at', Carbon::now()->year)
                ->count();
            $currentMonthViews = Post::whereMonth('created_at', Carbon::now()->month)
                ->whereYear('created_at', Carbon::now()->year)
                ->sum('views');
            $currentMonthComments = Comment::whereMonth('created_at', Carbon::now()->month)
                ->whereYear('created_at', Carbon::now()->year)
                ->count();

            $lastMonthPosts = Post::whereMonth('created_at', Carbon::now()->subMonth()->month)
                ->whereYear('created_at', Carbon::now()->subMonth()->year)
                ->count();
            $lastMonthViews = Post::whereMonth('created_at', Carbon::now()->subMonth()->month)
                ->whereYear('created_at', Carbon::now()->subMonth()->year)
                ->sum('views');
            $lastMonthComments = Comment::whereMonth('created_at', Carbon::now()->subMonth()->month)
                ->whereYear('created_at', Carbon::now()->subMonth()->year)
                ->count();

            $postsPercentage = $this->calculatePercentageChange($lastMonthPosts, $currentMonthPosts);
            $viewsPercentage = $this->calculatePercentageChange($lastMonthViews, $currentMonthViews);
            $commentsPercentage = $this->calculatePercentageChange($lastMonthComments, $currentMonthComments);

            $postStats = [
                'total_posts' => Post::count(),
                'total_views' => Post::sum('views'),
                'total_comments' => Post::withCount('comments')->get()->sum('comments_count'),
                'posts_percentage' => $postsPercentage,
                'views_percentage' => $viewsPercentage,
                'comments_percentage' => $commentsPercentage,
            ];
        } else {
            $currentMonthPosts = Post::where('user_id', $user->id)
                ->whereMonth('created_at', Carbon::now()->month)
                ->whereYear('created_at', Carbon::now()->year)
                ->count();
            $currentMonthViews = Post::where('user_id', $user->id)
                ->whereMonth('created_at', Carbon::now()->month)
                ->whereYear('created_at', Carbon::now()->year)
                ->sum('views');
            $currentMonthComments = Comment::where('user_id', $user->id)
                ->whereMonth('created_at', Carbon::now()->month)
                ->whereYear('created_at', Carbon::now()->year)
                ->count();

            $lastMonthPosts = Post::where('user_id', $user->id)
                ->whereMonth('created_at', Carbon::now()->subMonth()->month)
                ->whereYear('created_at', Carbon::now()->subMonth()->year)
                ->count();
            $lastMonthViews = Post::where('user_id', $user->id)
                ->whereMonth('created_at', Carbon::now()->subMonth()->month)
                ->whereYear('created_at', Carbon::now()->subMonth()->year)
                ->sum('views');
            $lastMonthComments = Comment::where('user_id', $user->id)
                ->whereMonth('created_at', Carbon::now()->subMonth()->month)
                ->whereYear('created_at', Carbon::now()->subMonth()->year)
                ->count();

            $postsPercentage = $this->calculatePercentageChange($lastMonthPosts, $currentMonthPosts);
            $viewsPercentage = $this->calculatePercentageChange($lastMonthViews, $currentMonthViews);
            $commentsPercentage = $this->calculatePercentageChange($lastMonthComments, $currentMonthComments);

            $userPosts = Post::where('user_id', $user->id)->get();
            $postStats = [
                'total_posts' => $userPosts->count(),
                'total_views' => $userPosts->sum('views'),
                'total_comments' => $userPosts->loadCount('comments')->sum('comments_count'),
                'posts_percentage' => $postsPercentage,
                'views_percentage' => $viewsPercentage,
                'comments_percentage' => $commentsPercentage,
            ];
        }

        $chartData = $this->getChartData($isAdmin, $user->id);

        $adminStats = [];
        if ($isAdmin) {
            $adminStats = [
                'top_users' => User::withCount(['posts', 'comments'])
                    ->orderByDesc('posts_count')
                    ->limit(5)
                    ->get()
                    ->map(function ($user) {
                        return [
                            'name' => $user->name,
                            'posts' => $user->posts_count,
                            'comments' => $user->comments_count,
                            'views' => $user->posts()->sum('views')
                        ];
                    }),
                'active_users' => User::withCount('posts')
                    ->orderByDesc('posts_count')
                    ->limit(5)
                    ->get()
                    ->map(function ($user) {
                        return [
                            'name' => $user->name,
                            'posts' => $user->posts_count
                        ];
                    })
            ];
        }

        $userPanelData = [];
        if (!$isAdmin) {
            $userPanelData = [
                'recent_activities' => $this->getUserRecentActivities($user->id),
                'achievements' => $this->getUserAchievements($user->id),
                'top_posts' => $this->getUserTopPosts($user->id),
            ];
        } else {
            $topViewsPost = Post::orderBy('views', 'desc')->first();
            $topCommentsPost = Post::withCount('comments')->orderBy('comments_count', 'desc')->first();
            $topLikesPost = Post::orderBy('likes', 'desc')->first();

            $userPanelData = [
                'top_posts' => [
                    'top_views' => [
                        'title' => $topViewsPost ? ($topViewsPost->title ?: 'Tanpa judul') : 'Belum ada postingan',
                        'count' => $topViewsPost ? $topViewsPost->views : 0,
                    ],
                    'top_comments' => [
                        'title' => $topCommentsPost ? ($topCommentsPost->title ?: 'Tanpa judul') : 'Belum ada postingan',
                        'count' => $topCommentsPost ? $topCommentsPost->comments_count : 0,
                    ],
                    'top_likes' => [
                        'title' => $topLikesPost ? ($topLikesPost->title ?: 'Tanpa judul') : 'Belum ada postingan',
                        'count' => $topLikesPost ? $topLikesPost->likes : 0,
                    ]
                ]
            ];
        }

        // dd($userPanelData);

        return view('app.dashboard.index', compact(
            'user',
            'postStats',
            'chartData',
            'adminStats',
            'userPanelData'
        ));
    }

    protected function getChartData($isAdmin = false, $userId = null)
    {
        $postQuery = Post::selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->whereYear('created_at', date('Y'));

        if (!$isAdmin && $userId) {
            $postQuery->where('user_id', $userId);
        }

        $postChart = $postQuery->groupBy('month')
            ->orderBy('month')
            ->get();

        $viewsPerMonth = [];
        $commentsPerMonth = [];
        $likesPerMonth = [];
        $topPostsPerMonth = [];
        $topPostsTitlesPerMonth = [];
        $topCommentedPostsTitlesPerMonth = [];
        $topLikedPostsTitlesPerMonth = [];

        $currentMonth = Carbon::now()->month;
        $weeksInMonth = [];
        $weeklyData = [
            'views' => [],
            'comments' => [],
            'likes' => [],
            'top_posts' => [],
            'top_posts_titles' => [],
            'top_commented_posts_titles' => [],
            'top_liked_posts_titles' => []
        ];

        for ($week = 1; $week <= 4; $week++) {
            $weeksInMonth[] = "Pekan $week";

            $startDay = ($week - 1) * 7 + 1;
            $endDay = $week * 7;

            $startDate = Carbon::createFromDate(date('Y'), $currentMonth, $startDay)->startOfDay();
            $endDate = Carbon::createFromDate(date('Y'), $currentMonth, $endDay)->endOfDay();

            if ($endDay > Carbon::createFromDate(date('Y'), $currentMonth, 1)->daysInMonth) {
                $endDate = Carbon::createFromDate(date('Y'), $currentMonth, 1)->endOfMonth()->endOfDay();
            }

            $weekPosts = Post::whereBetween('created_at', [$startDate, $endDate]);

            if (!$isAdmin && $userId) {
                $weekPosts->where('user_id', $userId);
            }

            $posts = $weekPosts->get();

            $weeklyData['views'][$week] = $posts->sum('views');
            $weeklyData['comments'][$week] = 0;
            $weeklyData['likes'][$week] = 0;

            foreach ($posts as $post) {
                $weeklyData['comments'][$week] += $post->comments()->count();
                $weeklyData['likes'][$week] += $post->likes ?? 0;
            }

            $topPost = Post::whereBetween('created_at', [$startDate, $endDate]);

            if (!$isAdmin && $userId) {
                $topPost->where('user_id', $userId);
            }

            $topPost = $topPost->orderBy('views', 'desc')->first();

            $weeklyData['top_posts'][$week] = $topPost ? $topPost->views : 0;
            $weeklyData['top_posts_titles'][$week] = $topPost ? $topPost->title : '-';

            $topCommentedPost = Post::whereBetween('created_at', [$startDate, $endDate])
                ->withCount('comments');

            if (!$isAdmin && $userId) {
                $topCommentedPost->where('user_id', $userId);
            }

            $topCommentedPost = $topCommentedPost->orderBy('comments_count', 'desc')->first();

            $weeklyData['top_commented_posts_titles'][$week] = $topCommentedPost ? $topCommentedPost->title : '-';

            $topLikedPost = Post::whereBetween('created_at', [$startDate, $endDate]);

            if (!$isAdmin && $userId) {
                $topLikedPost->where('user_id', $userId);
            }

            $topLikedPost = $topLikedPost->orderBy('likes', 'desc')->first();

            $weeklyData['top_liked_posts_titles'][$week] = $topLikedPost ? $topLikedPost->title : '-';
        }

        $lastThreeMonths = [];
        $quarterlyData = [
            'views' => [],
            'comments' => [],
            'likes' => [],
            'top_posts' => [],
            'top_posts_titles' => [],
            'top_commented_posts_titles' => [],
            'top_liked_posts_titles' => []
        ];

        for ($i = 2; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i)->month;
            $monthName = Carbon::now()->subMonths($i)->locale('id')->monthName;
            $lastThreeMonths[] = $monthName;

            $startDate = Carbon::createFromDate(date('Y'), $month, 1)->startOfMonth();
            $endDate = Carbon::createFromDate(date('Y'), $month, 1)->endOfMonth();

            $monthPosts = Post::whereBetween('created_at', [$startDate, $endDate]);

            if (!$isAdmin && $userId) {
                $monthPosts->where('user_id', $userId);
            }

            $posts = $monthPosts->get();

            $quarterlyData['views'][$month] = $posts->sum('views');
            $quarterlyData['comments'][$month] = 0;
            $quarterlyData['likes'][$month] = 0;

            foreach ($posts as $post) {
                $quarterlyData['comments'][$month] += $post->comments()->count();
                $quarterlyData['likes'][$month] += $post->likes ?? 0;
            }

            $topPost = Post::whereBetween('created_at', [$startDate, $endDate]);

            if (!$isAdmin && $userId) {
                $topPost->where('user_id', $userId);
            }

            $topPost = $topPost->orderBy('views', 'desc')->first();

            $quarterlyData['top_posts'][$month] = $topPost ? $topPost->views : 0;
            $quarterlyData['top_posts_titles'][$month] = $topPost ? $topPost->title : '-';

            $topCommentedPost = Post::whereBetween('created_at', [$startDate, $endDate])
                ->withCount('comments');

            if (!$isAdmin && $userId) {
                $topCommentedPost->where('user_id', $userId);
            }

            $topCommentedPost = $topCommentedPost->orderBy('comments_count', 'desc')->first();

            $quarterlyData['top_commented_posts_titles'][$month] = $topCommentedPost ? $topCommentedPost->title : '-';

            $topLikedPost = Post::whereBetween('created_at', [$startDate, $endDate]);

            if (!$isAdmin && $userId) {
                $topLikedPost->where('user_id', $userId);
            }

            $topLikedPost = $topLikedPost->orderBy('likes', 'desc')->first();

            $quarterlyData['top_liked_posts_titles'][$month] = $topLikedPost ? $topLikedPost->title : '-';
        }

        for ($i = 1; $i <= 12; $i++) {
            $monthPosts = Post::whereMonth('created_at', $i)
                ->whereYear('created_at', date('Y'));

            if (!$isAdmin && $userId) {
                $monthPosts->where('user_id', $userId);
            }

            $posts = $monthPosts->get();

            $viewsPerMonth[$i] = $posts->sum('views');
            $commentsPerMonth[$i] = 0;
            $likesPerMonth[$i] = 0;

            foreach ($posts as $post) {
                $commentsPerMonth[$i] += $post->comments()->count();
                $likesPerMonth[$i] += $post->likes ?? 0;
            }

            $topPost = Post::whereMonth('created_at', $i)
                ->whereYear('created_at', date('Y'));

            if (!$isAdmin && $userId) {
                $topPost->where('user_id', $userId);
            }

            $topPost = $topPost->orderBy('views', 'desc')->first();

            $topPostsPerMonth[$i] = $topPost ? $topPost->views : 0;
            $topPostsTitlesPerMonth[$i] = $topPost ? $topPost->title : '-';

            $topCommentedPost = Post::whereMonth('created_at', $i)
                ->whereYear('created_at', date('Y'))
                ->withCount('comments');

            if (!$isAdmin && $userId) {
                $topCommentedPost->where('user_id', $userId);
            }

            $topCommentedPost = $topCommentedPost->orderBy('comments_count', 'desc')->first();

            $topCommentedPostsTitlesPerMonth[$i] = $topCommentedPost ? $topCommentedPost->title : '-';

            $topLikedPost = Post::whereMonth('created_at', $i)
                ->whereYear('created_at', date('Y'));

            if (!$isAdmin && $userId) {
                $topLikedPost->where('user_id', $userId);
            }

            $topLikedPost = $topLikedPost->orderBy('likes', 'desc')->first();

            $topLikedPostsTitlesPerMonth[$i] = $topLikedPost ? $topLikedPost->title : '-';
        }

        return [
            'post_chart' => [
                'labels' => $postChart->pluck('month')->map(function ($month) {
                    return date('F', mktime(0, 0, 0, $month, 1));
                }),
                'data' => $postChart->pluck('count')
            ],
            'activity_chart' => [
                'labels' => ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu', 'Minggu'],
                'data' => [10, 20, 30, 40, 50, 60, 70]
            ],
            'monthly_views_chart' => [
                'labels' => range(1, 12),
                'data' => collect(range(1, 12))->map(function ($month) use ($viewsPerMonth) {
                    return $viewsPerMonth[$month] ?? 0;
                })
            ],
            'monthly_comments_chart' => [
                'labels' => range(1, 12),
                'data' => collect(range(1, 12))->map(function ($month) use ($commentsPerMonth) {
                    return $commentsPerMonth[$month] ?? 0;
                })
            ],
            'monthly_likes_chart' => [
                'labels' => range(1, 12),
                'data' => collect(range(1, 12))->map(function ($month) use ($likesPerMonth) {
                    return $likesPerMonth[$month] ?? 0;
                })
            ],
            'top_posts_chart' => [
                'labels' => range(1, 12),
                'data' => collect(range(1, 12))->map(function ($month) use ($topPostsPerMonth) {
                    return $topPostsPerMonth[$month] ?? 0;
                })
            ],
            'top_posts_titles' => collect(range(1, 12))->map(function ($month) use ($topPostsTitlesPerMonth) {
                return $topPostsTitlesPerMonth[$month] ?? '-';
            }),
            'top_commented_posts_titles' => collect(range(1, 12))->map(function ($month) use ($topCommentedPostsTitlesPerMonth) {
                return $topCommentedPostsTitlesPerMonth[$month] ?? '-';
            }),
            'top_liked_posts_titles' => collect(range(1, 12))->map(function ($month) use ($topLikedPostsTitlesPerMonth) {
                return $topLikedPostsTitlesPerMonth[$month] ?? '-';
            }),
            'weekly_data' => [
                'labels' => $weeksInMonth,
                'views' => array_values($weeklyData['views']),
                'comments' => array_values($weeklyData['comments']),
                'likes' => array_values($weeklyData['likes']),
                'top_posts' => array_values($weeklyData['top_posts']),
                'top_posts_titles' => array_values($weeklyData['top_posts_titles']),
                'top_commented_posts_titles' => array_values($weeklyData['top_commented_posts_titles']),
                'top_liked_posts_titles' => array_values($weeklyData['top_liked_posts_titles'])
            ],
            'quarterly_data' => [
                'labels' => $lastThreeMonths,
                'views' => array_values($quarterlyData['views']),
                'comments' => array_values($quarterlyData['comments']),
                'likes' => array_values($quarterlyData['likes']),
                'top_posts' => array_values($quarterlyData['top_posts']),
                'top_posts_titles' => array_values($quarterlyData['top_posts_titles']),
                'top_commented_posts_titles' => array_values($quarterlyData['top_commented_posts_titles']),
                'top_liked_posts_titles' => array_values($quarterlyData['top_liked_posts_titles'])
            ]
        ];
    }

    protected function getUserRecentActivities($userId)
    {
        $recentPosts = Post::where('user_id', $userId)
            ->latest()
            ->limit(3)
            ->get();

        $recentComments = Comment::where('user_id', $userId)
            ->with('post')
            ->latest()
            ->limit(3)
            ->get();

        $activities = [];

        foreach ($recentPosts as $post) {
            $activities[] = [
                'type' => 'post',
                'title' => $post->title,
                'time' => $post->created_at,
            ];
        }

        foreach ($recentComments as $comment) {
            if ($comment->post) {
                $activities[] = [
                    'type' => 'comment',
                    'post_title' => $comment->post->title,
                    'time' => $comment->created_at,
                ];
            }
        }

        usort($activities, function ($a, $b) {
            return $b['time']->timestamp - $a['time']->timestamp;
        });

        return array_slice($activities, 0, 3);
    }

    protected function getUserAchievements($userId)
    {
        $user = User::find($userId);

        $postsCount = $user->posts()->count();
        $commentsCount = $user->comments()->count();
        $viewsCount = $user->posts()->sum('views');

        $achievements = [
            [
                'name' => 'Poster Pemula',
                'progress' => min(100, ($postsCount / 5) * 100),
                'completed' => $postsCount >= 5,
            ],
            [
                'name' => 'Komentator Aktif',
                'progress' => min(100, ($commentsCount / 10) * 100),
                'completed' => $commentsCount >= 10,
            ],
            [
                'name' => 'Pembaca Antusias',
                'progress' => min(100, ($viewsCount / 100) * 100),
                'completed' => $viewsCount >= 100,
            ],
        ];

        return $achievements;
    }

    protected function getUserTopPosts($userId)
    {
        $topViewsPost = Post::where('user_id', $userId)
            ->orderBy('views', 'desc')
            ->first();

        $topCommentsPost = Post::where('user_id', $userId)
            ->withCount('comments')
            ->orderBy('comments_count', 'desc')
            ->first();

        $topLikesPost = Post::where('user_id', $userId)
            ->orderBy('likes', 'desc')
            ->first();

        return [
            'top_views' => [
                'title' => $topViewsPost ? ($topViewsPost->title ?: 'Tanpa judul') : 'Belum ada postingan',
                'count' => $topViewsPost ? $topViewsPost->views : 0,
            ],
            'top_comments' => [
                'title' => $topCommentsPost ? ($topCommentsPost->title ?: 'Tanpa judul') : 'Belum ada postingan',
                'count' => $topCommentsPost ? $topCommentsPost->comments_count : 0,
            ],
            'top_likes' => [
                'title' => $topLikesPost ? ($topLikesPost->title ?: 'Tanpa judul') : 'Belum ada postingan',
                'count' => $topLikesPost ? $topLikesPost->likes : 0,
            ],
        ];
    }

    /**
     * Menghitung persentase perubahan antara dua nilai
     */
    private function calculatePercentageChange($oldValue, $newValue)
    {
        if ($oldValue == 0) {
            return $newValue > 0 ? 100 : 0;
        }

        return round((($newValue - $oldValue) / $oldValue) * 100, 1);
    }
}
