@props(['label', 'name', 'value', 'type' => 'text'])

<div>
    <label class="block text-sm font-medium text-gray-400">{{ $label }}</label>
    <div class="editable-field mt-1 relative">
        <span class="text-indigo-100 cursor-pointer pr-8">{{ $value }}</span>
        <i class="fas fa-pencil-alt text-gray-400 absolute right-2 top-1/2 -translate-y-1/2"></i>
        @if ($type === 'textarea')
            <textarea name="{{ $name }}" class="hidden w-full bg-gray-700 text-indigo-100 rounded px-3 py-2">{{ $value }}</textarea>
        @elseif($type === 'select')
            <select name="{{ $name }}" class="hidden w-full bg-gray-700 text-indigo-100 rounded px-3 py-2">
                <option value="{{ $value }}">{{ $value }}</option>
            </select>
        @else
            <input type="{{ $type }}" name="{{ $name }}" value="{{ $value }}"
                class="hidden w-full bg-gray-700 text-indigo-100 rounded px-3 py-2">
        @endif
    </div>
</div>
