<?php $__env->startSection('title', 'Buat Postingan Baru'); ?>

<?php $__env->startSection('content'); ?>
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <?php if (isset($component)) { $__componentOriginal84611961a38a3895bf77adee3ebcafc0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal84611961a38a3895bf77adee3ebcafc0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.section','data' => ['title' => 'Buat Postingan Baru']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Buat Postingan Baru']); ?>
            <div id="check-requirements">
                <?php
                    $categoriesCount = \App\Models\Category::count();
                    $tagsCount = \App\Models\Tag::count();
                    $canCreatePost = $categoriesCount > 0 && $tagsCount > 0;
                ?>

                <?php if(!$canCreatePost): ?>
                    <div class="p-5 bg-gray-900/50 rounded-lg">
                        <h3 class="text-lg font-medium text-white mb-4">Persyaratan Postingan</h3>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div
                                    class="flex-shrink-0 h-6 w-6 <?php echo e($categoriesCount > 0 ? 'text-green-500' : 'text-red-500'); ?>">
                                    <?php if($categoriesCount > 0): ?>
                                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 13l4 4L19 7" />
                                        </svg>
                                    <?php else: ?>
                                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-3">
                                    <h3
                                        class="text-sm font-medium <?php echo e($categoriesCount > 0 ? 'text-green-300' : 'text-red-300'); ?>">
                                        Kategori</h3>
                                    <div class="mt-1 text-sm text-gray-400">
                                        <?php if($categoriesCount > 0): ?>
                                            Tersedia <?php echo e($categoriesCount); ?> kategori. <a
                                                href="<?php echo e(route('categories.index')); ?>"
                                                class="text-indigo-400 hover:text-indigo-300">Lihat kategori</a>
                                        <?php else: ?>
                                            Belum ada kategori. <a href="<?php echo e(route('categories.create')); ?>"
                                                class="text-indigo-400 hover:text-indigo-300">Buat kategori</a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-6 w-6 <?php echo e($tagsCount > 0 ? 'text-green-500' : 'text-red-500'); ?>">
                                    <?php if($tagsCount > 0): ?>
                                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 13l4 4L19 7" />
                                        </svg>
                                    <?php else: ?>
                                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-3">
                                    <h3
                                        class="text-sm font-medium <?php echo e($tagsCount > 0 ? 'text-green-300' : 'text-red-300'); ?>">
                                        Tag</h3>
                                    <div class="mt-1 text-sm text-gray-400">
                                        <?php if($tagsCount > 0): ?>
                                            Tersedia <?php echo e($tagsCount); ?> tag. <a href="<?php echo e(route('tags.index')); ?>"
                                                class="text-indigo-400 hover:text-indigo-300">Lihat tag</a>
                                        <?php else: ?>
                                            Belum ada tag. <a href="<?php echo e(route('tags.create')); ?>"
                                                class="text-indigo-400 hover:text-indigo-300">Buat tag</a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6">
                            <p class="text-sm text-gray-300">Anda perlu membuat minimal satu kategori dan satu tag sebelum
                                dapat membuat postingan.</p>
                        </div>
                    </div>
                <?php else: ?>
                    <form action="<?php echo e(route('posts.store')); ?>" method="POST" enctype="multipart/form-data"
                        class="space-y-6">
                        <?php echo csrf_field(); ?>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="md:col-span-2">
                                <?php if (isset($component)) { $__componentOriginal5c2a97ab476b69c1189ee85d1a95204b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal5c2a97ab476b69c1189ee85d1a95204b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.input','data' => ['name' => 'title','label' => 'Judul Postingan','placeholder' => 'Masukkan judul...','required' => true,'error' => $errors->first('title')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('form.input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'title','label' => 'Judul Postingan','placeholder' => 'Masukkan judul...','required' => true,'error' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($errors->first('title'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal5c2a97ab476b69c1189ee85d1a95204b)): ?>
<?php $attributes = $__attributesOriginal5c2a97ab476b69c1189ee85d1a95204b; ?>
<?php unset($__attributesOriginal5c2a97ab476b69c1189ee85d1a95204b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal5c2a97ab476b69c1189ee85d1a95204b)): ?>
<?php $component = $__componentOriginal5c2a97ab476b69c1189ee85d1a95204b; ?>
<?php unset($__componentOriginal5c2a97ab476b69c1189ee85d1a95204b); ?>
<?php endif; ?>
                            </div>

                            <div class="md:col-span-2">
                                <?php if (isset($component)) { $__componentOriginalcd97a59301ba78d56b3ed60dd41409ab = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalcd97a59301ba78d56b3ed60dd41409ab = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.textarea','data' => ['name' => 'description','label' => 'Deskripsi Singkat','placeholder' => 'Tuliskan deskripsi singkat postingan...','rows' => '3','required' => true,'error' => $errors->first('description')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('form.textarea'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'description','label' => 'Deskripsi Singkat','placeholder' => 'Tuliskan deskripsi singkat postingan...','rows' => '3','required' => true,'error' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($errors->first('description'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalcd97a59301ba78d56b3ed60dd41409ab)): ?>
<?php $attributes = $__attributesOriginalcd97a59301ba78d56b3ed60dd41409ab; ?>
<?php unset($__attributesOriginalcd97a59301ba78d56b3ed60dd41409ab); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalcd97a59301ba78d56b3ed60dd41409ab)): ?>
<?php $component = $__componentOriginalcd97a59301ba78d56b3ed60dd41409ab; ?>
<?php unset($__componentOriginalcd97a59301ba78d56b3ed60dd41409ab); ?>
<?php endif; ?>
                            </div>

                            <div class="md:col-span-2">
                                <?php if (isset($component)) { $__componentOriginal4ef915f7310b621e686fb279a9a0c0ee = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4ef915f7310b621e686fb279a9a0c0ee = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.ckeditor-textarea','data' => ['name' => 'content','label' => 'Konten','placeholder' => 'Tulis konten postingan anda disini...','rows' => '8','required' => true,'error' => $errors->first('content'),'value' => old('content')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('form.ckeditor-textarea'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'content','label' => 'Konten','placeholder' => 'Tulis konten postingan anda disini...','rows' => '8','required' => true,'error' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($errors->first('content')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('content'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4ef915f7310b621e686fb279a9a0c0ee)): ?>
<?php $attributes = $__attributesOriginal4ef915f7310b621e686fb279a9a0c0ee; ?>
<?php unset($__attributesOriginal4ef915f7310b621e686fb279a9a0c0ee); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4ef915f7310b621e686fb279a9a0c0ee)): ?>
<?php $component = $__componentOriginal4ef915f7310b621e686fb279a9a0c0ee; ?>
<?php unset($__componentOriginal4ef915f7310b621e686fb279a9a0c0ee); ?>
<?php endif; ?>
                            </div>

                            <div class="md:col-span-2">
                                <?php
                                    $categoryOptions = $categories
                                        ->map(function ($category) {
                                            return [
                                                'id' => $category->id,
                                                'name' => $category->title,
                                                'description' => $category->description,
                                                'image' => $category->banner,
                                            ];
                                        })
                                        ->toArray();
                                ?>

                                <?php if (isset($component)) { $__componentOriginalca0217661bc5c98c2e71d2f3864e3d28 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalca0217661bc5c98c2e71d2f3864e3d28 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.radio-group','data' => ['name' => 'category_id','label' => 'Pilih Kategori','options' => $categoryOptions,'selected' => old('category_id')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('form.radio-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'category_id','label' => 'Pilih Kategori','options' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($categoryOptions),'selected' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('category_id'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalca0217661bc5c98c2e71d2f3864e3d28)): ?>
<?php $attributes = $__attributesOriginalca0217661bc5c98c2e71d2f3864e3d28; ?>
<?php unset($__attributesOriginalca0217661bc5c98c2e71d2f3864e3d28); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalca0217661bc5c98c2e71d2f3864e3d28)): ?>
<?php $component = $__componentOriginalca0217661bc5c98c2e71d2f3864e3d28; ?>
<?php unset($__componentOriginalca0217661bc5c98c2e71d2f3864e3d28); ?>
<?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('dashboard-admin')): ?>
                                    <div class="text-right mt-2">
                                        <a href="<?php echo e(route('categories.index')); ?>"
                                            class="text-xs text-indigo-400 hover:text-indigo-300">
                                            Kelola kategori
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="md:col-span-2">
                                <?php
                                    $tagOptions = $tags
                                        ->map(function ($tag) {
                                            return [
                                                'id' => $tag->id,
                                                'name' => $tag->title,
                                                'description' => $tag->description,
                                            ];
                                        })
                                        ->toArray();
                                ?>

                                <?php if (isset($component)) { $__componentOriginalb8bce20b6320ce0c036bb9cf4ec58544 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb8bce20b6320ce0c036bb9cf4ec58544 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.checkbox-group','data' => ['name' => 'tags','label' => 'Pilih Tag (Maksimal 3)','options' => $tagOptions,'selected' => $selectedTags,'max' => '3']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('form.checkbox-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'tags','label' => 'Pilih Tag (Maksimal 3)','options' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($tagOptions),'selected' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($selectedTags),'max' => '3']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb8bce20b6320ce0c036bb9cf4ec58544)): ?>
<?php $attributes = $__attributesOriginalb8bce20b6320ce0c036bb9cf4ec58544; ?>
<?php unset($__attributesOriginalb8bce20b6320ce0c036bb9cf4ec58544); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb8bce20b6320ce0c036bb9cf4ec58544)): ?>
<?php $component = $__componentOriginalb8bce20b6320ce0c036bb9cf4ec58544; ?>
<?php unset($__componentOriginalb8bce20b6320ce0c036bb9cf4ec58544); ?>
<?php endif; ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('dashboard-admin')): ?>
                                    <div class="text-right mt-2">
                                        <a href="<?php echo e(route('tags.index')); ?>"
                                            class="text-xs text-indigo-400 hover:text-indigo-300">
                                            Kelola tag
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="md:col-span-2">
                                <?php if (isset($component)) { $__componentOriginalb325d6299e3e80731b0f9f64a9bce8ed = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb325d6299e3e80731b0f9f64a9bce8ed = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.file-upload','data' => ['name' => 'image','label' => 'Gambar Postingan','accept' => 'image/jpeg,image/png,image/gif','error' => $errors->first('image'),'helper' => 'JPG, PNG, atau GIF. Maksimal 2MB.']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('form.file-upload'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'image','label' => 'Gambar Postingan','accept' => 'image/jpeg,image/png,image/gif','error' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($errors->first('image')),'helper' => 'JPG, PNG, atau GIF. Maksimal 2MB.']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb325d6299e3e80731b0f9f64a9bce8ed)): ?>
<?php $attributes = $__attributesOriginalb325d6299e3e80731b0f9f64a9bce8ed; ?>
<?php unset($__attributesOriginalb325d6299e3e80731b0f9f64a9bce8ed); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb325d6299e3e80731b0f9f64a9bce8ed)): ?>
<?php $component = $__componentOriginalb325d6299e3e80731b0f9f64a9bce8ed; ?>
<?php unset($__componentOriginalb325d6299e3e80731b0f9f64a9bce8ed); ?>
<?php endif; ?>
                            </div>
                        </div>

                        <div class="flex justify-end pt-6 border-t border-gray-700">
                            <?php if (isset($component)) { $__componentOriginal8a31ff0802d1df0c26bb607f30439b3a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a31ff0802d1df0c26bb607f30439b3a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.button','data' => ['onclick' => 'window.history.back()','type' => 'button','variant' => 'secondary','class' => 'mr-2']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('form.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['onclick' => 'window.history.back()','type' => 'button','variant' => 'secondary','class' => 'mr-2']); ?>
                                Batal
                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a31ff0802d1df0c26bb607f30439b3a)): ?>
<?php $attributes = $__attributesOriginal8a31ff0802d1df0c26bb607f30439b3a; ?>
<?php unset($__attributesOriginal8a31ff0802d1df0c26bb607f30439b3a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a31ff0802d1df0c26bb607f30439b3a)): ?>
<?php $component = $__componentOriginal8a31ff0802d1df0c26bb607f30439b3a; ?>
<?php unset($__componentOriginal8a31ff0802d1df0c26bb607f30439b3a); ?>
<?php endif; ?>

                            <?php if (isset($component)) { $__componentOriginal8a31ff0802d1df0c26bb607f30439b3a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a31ff0802d1df0c26bb607f30439b3a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.form.button','data' => ['type' => 'submit','variant' => 'primary']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('form.button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit','variant' => 'primary']); ?>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                                Simpan Postingan
                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a31ff0802d1df0c26bb607f30439b3a)): ?>
<?php $attributes = $__attributesOriginal8a31ff0802d1df0c26bb607f30439b3a; ?>
<?php unset($__attributesOriginal8a31ff0802d1df0c26bb607f30439b3a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a31ff0802d1df0c26bb607f30439b3a)): ?>
<?php $component = $__componentOriginal8a31ff0802d1df0c26bb607f30439b3a; ?>
<?php unset($__componentOriginal8a31ff0802d1df0c26bb607f30439b3a); ?>
<?php endif; ?>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal84611961a38a3895bf77adee3ebcafc0)): ?>
<?php $attributes = $__attributesOriginal84611961a38a3895bf77adee3ebcafc0; ?>
<?php unset($__attributesOriginal84611961a38a3895bf77adee3ebcafc0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal84611961a38a3895bf77adee3ebcafc0)): ?>
<?php $component = $__componentOriginal84611961a38a3895bf77adee3ebcafc0; ?>
<?php unset($__componentOriginal84611961a38a3895bf77adee3ebcafc0); ?>
<?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.appLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/app/posts/create.blade.php ENDPATH**/ ?>