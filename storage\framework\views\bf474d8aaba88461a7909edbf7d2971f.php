<?php $__env->startSection('title', 'Profile'); ?>

<?php $__env->startSection('content'); ?>
    <div class="max-w-4xl mx-auto py-6 sm:py-10 px-4 sm:px-6 lg:px-8">
        <div class="bg-gray-800 shadow-xl rounded-lg overflow-hidden">
            <!-- Profile Header -->
            <?php if (isset($component)) { $__componentOriginal08aa32d40a14740a91e7e6c41885cedd = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal08aa32d40a14740a91e7e6c41885cedd = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.profile.header','data' => ['name' => $user->name,'email' => $user->email]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('profile.header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user->name),'email' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user->email)]); ?>
                 <?php $__env->slot('slot', null, []); ?> 
                    <?php if (isset($component)) { $__componentOriginal94c03172b784fb0fd69f0202adcb79e8 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal94c03172b784fb0fd69f0202adcb79e8 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.profile.avatar-enhanced','data' => ['user' => $user,'profile' => $profile]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('profile.avatar-enhanced'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user),'profile' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($profile)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal94c03172b784fb0fd69f0202adcb79e8)): ?>
<?php $attributes = $__attributesOriginal94c03172b784fb0fd69f0202adcb79e8; ?>
<?php unset($__attributesOriginal94c03172b784fb0fd69f0202adcb79e8); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal94c03172b784fb0fd69f0202adcb79e8)): ?>
<?php $component = $__componentOriginal94c03172b784fb0fd69f0202adcb79e8; ?>
<?php unset($__componentOriginal94c03172b784fb0fd69f0202adcb79e8); ?>
<?php endif; ?>
                 <?php $__env->endSlot(); ?>

                 <?php $__env->slot('actions', null, []); ?> 
                    <button
                        class="text-sm px-3 py-1 bg-indigo-600 text-white rounded-full hover:bg-indigo-700 transition-colors duration-200 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        Pengaturan
                    </button>
                 <?php $__env->endSlot(); ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal08aa32d40a14740a91e7e6c41885cedd)): ?>
<?php $attributes = $__attributesOriginal08aa32d40a14740a91e7e6c41885cedd; ?>
<?php unset($__attributesOriginal08aa32d40a14740a91e7e6c41885cedd); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal08aa32d40a14740a91e7e6c41885cedd)): ?>
<?php $component = $__componentOriginal08aa32d40a14740a91e7e6c41885cedd; ?>
<?php unset($__componentOriginal08aa32d40a14740a91e7e6c41885cedd); ?>
<?php endif; ?>

            <!-- Profile Content -->
            <form id="profile-form" class="p-6" action="<?php echo e(route('profile.update')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <!-- Errors display -->
                <div id="validation-errors" class="mb-4 hidden">
                    <div class="bg-red-900/60 text-red-200 p-4 rounded-lg border border-red-800">
                        <div class="font-medium">Terjadi kesalahan validasi:</div>
                        <ul id="error-list" class="mt-2 text-sm list-disc list-inside"></ul>
                    </div>
                </div>

                <!-- Personal Information -->
                <?php if (isset($component)) { $__componentOriginalbd9dbb0261a3a8ae0f598d73c7e14c95 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd9dbb0261a3a8ae0f598d73c7e14c95 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.profile.section','data' => ['title' => 'Informasi Personal']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('profile.section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Informasi Personal']); ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <?php if (isset($component)) { $__componentOriginal2361dee330716bdb7b57d2eba36909ea = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2361dee330716bdb7b57d2eba36909ea = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.profile.editable-field-enhanced','data' => ['label' => 'Alamat','name' => 'address','value' => $profile->address]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('profile.editable-field-enhanced'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Alamat','name' => 'address','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($profile->address)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2361dee330716bdb7b57d2eba36909ea)): ?>
<?php $attributes = $__attributesOriginal2361dee330716bdb7b57d2eba36909ea; ?>
<?php unset($__attributesOriginal2361dee330716bdb7b57d2eba36909ea); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2361dee330716bdb7b57d2eba36909ea)): ?>
<?php $component = $__componentOriginal2361dee330716bdb7b57d2eba36909ea; ?>
<?php unset($__componentOriginal2361dee330716bdb7b57d2eba36909ea); ?>
<?php endif; ?>
                        <?php if (isset($component)) { $__componentOriginal2361dee330716bdb7b57d2eba36909ea = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2361dee330716bdb7b57d2eba36909ea = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.profile.editable-field-enhanced','data' => ['label' => 'Provinsi','name' => 'province','value' => $profile->province,'type' => 'select']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('profile.editable-field-enhanced'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Provinsi','name' => 'province','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($profile->province),'type' => 'select']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2361dee330716bdb7b57d2eba36909ea)): ?>
<?php $attributes = $__attributesOriginal2361dee330716bdb7b57d2eba36909ea; ?>
<?php unset($__attributesOriginal2361dee330716bdb7b57d2eba36909ea); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2361dee330716bdb7b57d2eba36909ea)): ?>
<?php $component = $__componentOriginal2361dee330716bdb7b57d2eba36909ea; ?>
<?php unset($__componentOriginal2361dee330716bdb7b57d2eba36909ea); ?>
<?php endif; ?>
                        <?php if (isset($component)) { $__componentOriginal2361dee330716bdb7b57d2eba36909ea = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2361dee330716bdb7b57d2eba36909ea = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.profile.editable-field-enhanced','data' => ['label' => 'Kota','name' => 'city','value' => $profile->city,'type' => 'select']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('profile.editable-field-enhanced'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Kota','name' => 'city','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($profile->city),'type' => 'select']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2361dee330716bdb7b57d2eba36909ea)): ?>
<?php $attributes = $__attributesOriginal2361dee330716bdb7b57d2eba36909ea; ?>
<?php unset($__attributesOriginal2361dee330716bdb7b57d2eba36909ea); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2361dee330716bdb7b57d2eba36909ea)): ?>
<?php $component = $__componentOriginal2361dee330716bdb7b57d2eba36909ea; ?>
<?php unset($__componentOriginal2361dee330716bdb7b57d2eba36909ea); ?>
<?php endif; ?>
                        <?php if (isset($component)) { $__componentOriginal2361dee330716bdb7b57d2eba36909ea = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2361dee330716bdb7b57d2eba36909ea = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.profile.editable-field-enhanced','data' => ['label' => 'Nomor Telepon','name' => 'phone','value' => $profile->phone]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('profile.editable-field-enhanced'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Nomor Telepon','name' => 'phone','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($profile->phone)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2361dee330716bdb7b57d2eba36909ea)): ?>
<?php $attributes = $__attributesOriginal2361dee330716bdb7b57d2eba36909ea; ?>
<?php unset($__attributesOriginal2361dee330716bdb7b57d2eba36909ea); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2361dee330716bdb7b57d2eba36909ea)): ?>
<?php $component = $__componentOriginal2361dee330716bdb7b57d2eba36909ea; ?>
<?php unset($__componentOriginal2361dee330716bdb7b57d2eba36909ea); ?>
<?php endif; ?>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd9dbb0261a3a8ae0f598d73c7e14c95)): ?>
<?php $attributes = $__attributesOriginalbd9dbb0261a3a8ae0f598d73c7e14c95; ?>
<?php unset($__attributesOriginalbd9dbb0261a3a8ae0f598d73c7e14c95); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd9dbb0261a3a8ae0f598d73c7e14c95)): ?>
<?php $component = $__componentOriginalbd9dbb0261a3a8ae0f598d73c7e14c95; ?>
<?php unset($__componentOriginalbd9dbb0261a3a8ae0f598d73c7e14c95); ?>
<?php endif; ?>

                <!-- Additional Information -->
                <?php if (isset($component)) { $__componentOriginalbd9dbb0261a3a8ae0f598d73c7e14c95 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalbd9dbb0261a3a8ae0f598d73c7e14c95 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.profile.section','data' => ['title' => 'Informasi Tambahan']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('profile.section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Informasi Tambahan']); ?>
                    <div class="space-y-6">
                        <?php if (isset($component)) { $__componentOriginal2361dee330716bdb7b57d2eba36909ea = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2361dee330716bdb7b57d2eba36909ea = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.profile.editable-field-enhanced','data' => ['label' => 'Hobi','name' => 'hobbies','value' => $profile->hobbies]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('profile.editable-field-enhanced'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Hobi','name' => 'hobbies','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($profile->hobbies)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2361dee330716bdb7b57d2eba36909ea)): ?>
<?php $attributes = $__attributesOriginal2361dee330716bdb7b57d2eba36909ea; ?>
<?php unset($__attributesOriginal2361dee330716bdb7b57d2eba36909ea); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2361dee330716bdb7b57d2eba36909ea)): ?>
<?php $component = $__componentOriginal2361dee330716bdb7b57d2eba36909ea; ?>
<?php unset($__componentOriginal2361dee330716bdb7b57d2eba36909ea); ?>
<?php endif; ?>
                        <?php if (isset($component)) { $__componentOriginal2361dee330716bdb7b57d2eba36909ea = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2361dee330716bdb7b57d2eba36909ea = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.profile.editable-field-enhanced','data' => ['label' => 'Bio','name' => 'bio','value' => $profile->bio,'type' => 'textarea']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('profile.editable-field-enhanced'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Bio','name' => 'bio','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($profile->bio),'type' => 'textarea']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2361dee330716bdb7b57d2eba36909ea)): ?>
<?php $attributes = $__attributesOriginal2361dee330716bdb7b57d2eba36909ea; ?>
<?php unset($__attributesOriginal2361dee330716bdb7b57d2eba36909ea); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2361dee330716bdb7b57d2eba36909ea)): ?>
<?php $component = $__componentOriginal2361dee330716bdb7b57d2eba36909ea; ?>
<?php unset($__componentOriginal2361dee330716bdb7b57d2eba36909ea); ?>
<?php endif; ?>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalbd9dbb0261a3a8ae0f598d73c7e14c95)): ?>
<?php $attributes = $__attributesOriginalbd9dbb0261a3a8ae0f598d73c7e14c95; ?>
<?php unset($__attributesOriginalbd9dbb0261a3a8ae0f598d73c7e14c95); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalbd9dbb0261a3a8ae0f598d73c7e14c95)): ?>
<?php $component = $__componentOriginalbd9dbb0261a3a8ae0f598d73c7e14c95; ?>
<?php unset($__componentOriginalbd9dbb0261a3a8ae0f598d73c7e14c95); ?>
<?php endif; ?>

                <!-- Save Button -->
                <div id="save-button" class="hidden mt-4">
                    <button type="submit"
                        class="w-full md:w-auto px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700
                        transition duration-200 flex items-center justify-center shadow-lg">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Simpan Semua Perubahan
                    </button>
                </div>
            </form>

            <!-- Account Statistics -->
            <div class="p-6 pt-0">
                <h2 class="text-xl font-semibold text-indigo-300 mb-4">Statistik Akun</h2>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <?php if (isset($component)) { $__componentOriginal29ea18046d24c5d7723830be65c03a83 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal29ea18046d24c5d7723830be65c03a83 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.profile.stats-card','data' => ['title' => 'Total Postingan','value' => $user->posts->count()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('profile.stats-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Total Postingan','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user->posts->count())]); ?>
                         <?php $__env->slot('icon', null, []); ?> 
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                            </svg>
                         <?php $__env->endSlot(); ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal29ea18046d24c5d7723830be65c03a83)): ?>
<?php $attributes = $__attributesOriginal29ea18046d24c5d7723830be65c03a83; ?>
<?php unset($__attributesOriginal29ea18046d24c5d7723830be65c03a83); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal29ea18046d24c5d7723830be65c03a83)): ?>
<?php $component = $__componentOriginal29ea18046d24c5d7723830be65c03a83; ?>
<?php unset($__componentOriginal29ea18046d24c5d7723830be65c03a83); ?>
<?php endif; ?>

                    <?php if (isset($component)) { $__componentOriginal29ea18046d24c5d7723830be65c03a83 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal29ea18046d24c5d7723830be65c03a83 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.profile.stats-card','data' => ['title' => 'Total Komentar','value' => $user->comments->count()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('profile.stats-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Total Komentar','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user->comments->count())]); ?>
                         <?php $__env->slot('icon', null, []); ?> 
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                            </svg>
                         <?php $__env->endSlot(); ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal29ea18046d24c5d7723830be65c03a83)): ?>
<?php $attributes = $__attributesOriginal29ea18046d24c5d7723830be65c03a83; ?>
<?php unset($__attributesOriginal29ea18046d24c5d7723830be65c03a83); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal29ea18046d24c5d7723830be65c03a83)): ?>
<?php $component = $__componentOriginal29ea18046d24c5d7723830be65c03a83; ?>
<?php unset($__componentOriginal29ea18046d24c5d7723830be65c03a83); ?>
<?php endif; ?>

                    <?php if (isset($component)) { $__componentOriginal29ea18046d24c5d7723830be65c03a83 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal29ea18046d24c5d7723830be65c03a83 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.profile.stats-card','data' => ['title' => 'Bergabung Sejak','value' => $user->created_at->diffForHumans()]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('profile.stats-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Bergabung Sejak','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user->created_at->diffForHumans())]); ?>
                         <?php $__env->slot('icon', null, []); ?> 
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                         <?php $__env->endSlot(); ?>
                         <?php $__env->slot('footer', null, []); ?> 
                            <div class="flex items-center justify-between">
                                <span>Tanggal daftar:</span>
                                <span><?php echo e($user->created_at->format('d M Y')); ?></span>
                            </div>
                         <?php $__env->endSlot(); ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal29ea18046d24c5d7723830be65c03a83)): ?>
<?php $attributes = $__attributesOriginal29ea18046d24c5d7723830be65c03a83; ?>
<?php unset($__attributesOriginal29ea18046d24c5d7723830be65c03a83); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal29ea18046d24c5d7723830be65c03a83)): ?>
<?php $component = $__componentOriginal29ea18046d24c5d7723830be65c03a83; ?>
<?php unset($__componentOriginal29ea18046d24c5d7723830be65c03a83); ?>
<?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                initializeProvinceCity();

                const form = document.getElementById('profile-form');
                const validationErrors = document.getElementById('validation-errors');
                const errorList = document.getElementById('error-list');

                form.addEventListener('submit', async (e) => {
                    e.preventDefault();

                    const formFields = ['address', 'province', 'city', 'phone', 'hobbies', 'bio'];
                    let allFieldsValid = true;

                    formFields.forEach(field => {
                        const input = form.querySelector(`[name="${field}"]`);
                        if (!input.value.trim()) {
                            allFieldsValid = false;

                            const fieldContainer = input.closest('.editable-field');
                            const displayValue = fieldContainer.querySelector('.display-value');
                            const editField = fieldContainer.querySelector('.edit-field');

                            displayValue.classList.add('hidden');
                            editField.classList.remove('hidden');

                            input.classList.add('border-red-500');
                        }
                    });

                    if (!allFieldsValid) {
                        validationErrors.classList.remove('hidden');
                        errorList.innerHTML = '<li>Semua field harus diisi</li>';
                        return;
                    }

                    try {
                        const formData = new FormData(form);

                        validationErrors.classList.add('hidden');
                        errorList.innerHTML = '';

                        const response = await fetch('<?php echo e(route('profile.update')); ?>', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest',
                                'Accept': 'application/json'
                            }
                        });

                        if (!response.ok) {
                            const data = await response.json();

                            if (data.errors) {
                                validationErrors.classList.remove('hidden');
                                errorList.innerHTML = '';

                                Object.keys(data.errors).forEach(field => {
                                    data.errors[field].forEach(message => {
                                        errorList.innerHTML += `<li>${message}</li>`;
                                    });

                                    const input = form.querySelector(`[name="${field}"]`);
                                    if (input) {
                                        input.classList.add('border-red-500');
                                    }
                                });
                                return;
                            }
                        }

                        const data = await response.json();

                        if (data.success) {
                            document.getElementById('save-button').classList.add('hidden');

                            form.querySelectorAll('input, select, textarea').forEach(input => {
                                input.classList.remove('border-red-500');
                            });

                            Swal.fire({
                                title: 'Berhasil!',
                                text: data.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });
                        } else {
                            Swal.fire({
                                title: 'Gagal!',
                                text: data.message || 'Terjadi kesalahan saat menyimpan perubahan.',
                                icon: 'error'
                            });
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        Swal.fire({
                            title: 'Error!',
                            text: 'Terjadi kesalahan saat menyimpan perubahan.',
                            icon: 'error'
                        });
                    }
                });

                async function initializeProvinceCity() {
                    console.log('Initializing province/city selects');
                    const provinceSelect = document.querySelector('select[name="province"]');
                    const citySelect = document.querySelector('select[name="city"]');

                    if (!provinceSelect || !citySelect) {
                        console.log('Select elements not found.');
                        return;
                    }

                    try {
                        console.log('Fetching provinces...');
                        const response = await fetch(
                            'https://www.emsifa.com/api-wilayah-indonesia/api/provinces.json');
                        const provinces = await response.json();
                        console.log('Provinces loaded:', provinces.length);

                        provinceSelect.innerHTML = '<option value="">Pilih Provinsi</option>';
                        provinces.forEach(province => {
                            const option = document.createElement('option');
                            option.value = province.name;
                            option.textContent = province.name;
                            option.dataset.id = province.id;
                            provinceSelect.appendChild(option);
                        });

                        const currentProvince = '<?php echo e($profile->province); ?>';
                        console.log('Current province:', currentProvince);
                        if (currentProvince) {
                            provinceSelect.value = currentProvince;

                            const selectedOption = Array.from(provinceSelect.options).find(opt => opt.value ===
                                currentProvince);
                            if (selectedOption && selectedOption.dataset.id) {
                                const provinceId = selectedOption.dataset.id;
                                console.log('Found province ID:', provinceId);
                                loadCities(provinceId, '<?php echo e($profile->city); ?>');
                            }
                        }

                        provinceSelect.addEventListener('change', async function() {
                            const selectedOption = this.options[this.selectedIndex];
                            const provinceId = selectedOption.dataset.id;
                            console.log('Province changed, selected ID:', provinceId);

                            if (!provinceId) return;

                            loadCities(provinceId);
                        });
                    } catch (error) {
                        console.error('Error fetching provinces:', error);
                    }
                }

                async function loadCities(provinceId, currentCity = '') {
                    console.log('Loading cities for province ID:', provinceId);
                    const citySelect = document.querySelector('select[name="city"]');
                    if (!citySelect) return;

                    try {
                        const response = await fetch(
                            `https://www.emsifa.com/api-wilayah-indonesia/api/regencies/${provinceId}.json`);
                        const cities = await response.json();
                        console.log('Cities loaded:', cities.length);

                        citySelect.innerHTML = '<option value="">Pilih Kota</option>';
                        cities.forEach(city => {
                            const option = document.createElement('option');
                            option.value = city.name;
                            option.textContent = city.name;
                            citySelect.appendChild(option);
                        });

                        if (currentCity) {
                            console.log('Setting current city:', currentCity);
                            citySelect.value = currentCity;
                        }
                    } catch (error) {
                        console.error('Error fetching cities:', error);
                    }
                }

                document.querySelectorAll('.edit-button').forEach(button => {
                    if (button.closest('.editable-field').querySelector('select[name="province"]')) {
                        button.addEventListener('click', () => {
                            console.log('Province field clicked, initializing selects');
                            setTimeout(initializeProvinceCity, 100);
                        });
                    }
                });

                form.querySelectorAll('input, select, textarea').forEach(input => {
                    input.addEventListener('focus', function() {
                        this.classList.remove('border-red-500');
                    });
                });
            });
        </script>
    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.appLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/app/profile/index.blade.php ENDPATH**/ ?>