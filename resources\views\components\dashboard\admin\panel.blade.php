@props(['stats'])

<div class="space-y-8 p-4">
    <h2 class="text-xl font-bold text-indigo-200 border-b border-gray-700 pb-2 flex items-center">
        <span class="mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
        </span>
        Admin Panel
    </h2>

    <!-- Dashboard Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div
            class="p-5 flex flex-col items-center justify-center bg-gradient-to-br from-blue-900/30 to-blue-800/50 rounded-xl shadow-lg border border-blue-800/30">
            <span class="text-blue-300 text-xs uppercase font-semibold tracking-wider">Total Pengguna</span>
            <span class="mt-1 text-3xl font-bold text-blue-100">{{ count($stats['top_users']) }}</span>
            <span class="mt-2 text-xs text-blue-300 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 text-green-400" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
                12% dari bulan lalu
            </span>
        </div>
        <div
            class="p-5 flex flex-col items-center justify-center bg-gradient-to-br from-indigo-900/30 to-indigo-800/50 rounded-xl shadow-lg border border-indigo-800/30">
            <span class="text-indigo-300 text-xs uppercase font-semibold tracking-wider">Total Post</span>
            <span class="mt-1 text-3xl font-bold text-indigo-100">
                @php
                    $totalPosts = 0;
                    foreach ($stats['top_users'] as $user) {
                        $totalPosts += $user['posts'];
                    }
                @endphp
                {{ $totalPosts }}
            </span>
            <span class="mt-2 text-xs text-indigo-300 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 text-green-400" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
                8% dari bulan lalu
            </span>
        </div>
        <div
            class="p-5 flex flex-col items-center justify-center bg-gradient-to-br from-purple-900/30 to-purple-800/50 rounded-xl shadow-lg border border-purple-800/30">
            <span class="text-purple-300 text-xs uppercase font-semibold tracking-wider">Total Komentar</span>
            <span class="mt-1 text-3xl font-bold text-purple-100">
                @php
                    $totalComments = 0;
                    foreach ($stats['top_users'] as $user) {
                        $totalComments += $user['comments'];
                    }
                @endphp
                {{ $totalComments }}
            </span>
            <span class="mt-2 text-xs text-purple-300 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 text-green-400" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
                15% dari bulan lalu
            </span>
        </div>
        <div
            class="p-5 flex flex-col items-center justify-center bg-gradient-to-br from-pink-900/30 to-pink-800/50 rounded-xl shadow-lg border border-pink-800/30">
            <span class="text-pink-300 text-xs uppercase font-semibold tracking-wider">Total Views</span>
            <span class="mt-1 text-3xl font-bold text-pink-100">
                @php
                    $totalViews = 0;
                    foreach ($stats['top_users'] as $user) {
                        $totalViews += $user['views'];
                    }
                @endphp
                {{ $totalViews }}
            </span>
            <span class="mt-2 text-xs text-pink-300 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 text-green-400" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
                22% dari bulan lalu
            </span>
        </div>
    </div>

    <!-- Admin Quick Actions -->
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
        <a href="#"
            class="p-5 bg-blue-600/20 border border-blue-600/40 rounded-xl flex flex-col items-center justify-center text-center hover:bg-blue-600/30 transition duration-300 group">
            <div
                class="w-12 h-12 rounded-full bg-blue-600/30 flex items-center justify-center mb-3 group-hover:bg-blue-600/50 transition duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
            </div>
            <span class="text-blue-200 font-medium">Kelola Pengguna</span>
        </a>
        <a href="#"
            class="p-5 bg-indigo-600/20 border border-indigo-600/40 rounded-xl flex flex-col items-center justify-center text-center hover:bg-indigo-600/30 transition duration-300 group">
            <div
                class="w-12 h-12 rounded-full bg-indigo-600/30 flex items-center justify-center mb-3 group-hover:bg-indigo-600/50 transition duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-400" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
            </div>
            <span class="text-indigo-200 font-medium">Kelola Postingan</span>
        </a>
        <a href="#"
            class="p-5 bg-purple-600/20 border border-purple-600/40 rounded-xl flex flex-col items-center justify-center text-center hover:bg-purple-600/30 transition duration-300 group">
            <div
                class="w-12 h-12 rounded-full bg-purple-600/30 flex items-center justify-center mb-3 group-hover:bg-purple-600/50 transition duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-400" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
            </div>
            <span class="text-purple-200 font-medium">Kelola Kategori</span>
        </a>
        <a href="#"
            class="p-5 bg-pink-600/20 border border-pink-600/40 rounded-xl flex flex-col items-center justify-center text-center hover:bg-pink-600/30 transition duration-300 group">
            <div
                class="w-12 h-12 rounded-full bg-pink-600/30 flex items-center justify-center mb-3 group-hover:bg-pink-600/50 transition duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-pink-400" fill="none"
                    viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
            </div>
            <span class="text-pink-200 font-medium">Pengaturan Sistem</span>
        </a>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 relative">
        <div
            class="p-6 bg-gradient-to-br from-indigo-900/40 to-indigo-800/40 rounded-xl border border-indigo-700/50 shadow-xl">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-indigo-100">User Teratas Berdasarkan Tontonan</h3>
                <div class="bg-indigo-900/60 text-xs text-indigo-300 py-1 px-2 rounded">Top 5</div>
            </div>
            <ul class="space-y-3">
                @foreach ($stats['top_users'] as $index => $user)
                    <li
                        class="flex items-center justify-between py-2 {{ $index < count($stats['top_users']) - 1 ? 'border-b border-indigo-800/50' : '' }}">
                        <div class="flex items-center">
                            <div
                                class="w-8 h-8 rounded-full bg-indigo-700/30 flex items-center justify-center text-indigo-200 text-xs font-bold mr-3">
                                {{ $index + 1 }}
                            </div>
                            <span class="text-indigo-100">{{ $user['name'] }}</span>
                        </div>
                        <div class="flex items-center">
                            <span class="text-indigo-300 font-semibold">{{ number_format($user['views']) }}</span>
                            <span class="text-xs text-indigo-400 ml-1">views</span>
                        </div>
                    </li>
                @endforeach
            </ul>
            <div class="mt-4 text-right">
                <a href="#" class="text-xs text-indigo-300 hover:text-indigo-100 flex items-center justify-end">
                    <span>Lihat Semua User</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </a>
            </div>
        </div>

        <div
            class="p-6 bg-gradient-to-br from-purple-900/40 to-purple-800/40 rounded-xl border border-purple-700/50 shadow-xl">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-purple-100">User Paling Aktif</h3>
                <div class="bg-purple-900/60 text-xs text-purple-300 py-1 px-2 rounded">Top 5</div>
            </div>
            <ul class="space-y-3">
                @foreach ($stats['active_users'] as $index => $user)
                    <li
                        class="flex items-center justify-between py-2 {{ $index < count($stats['active_users']) - 1 ? 'border-b border-purple-800/50' : '' }}">
                        <div class="flex items-center">
                            <div
                                class="w-8 h-8 rounded-full bg-purple-700/30 flex items-center justify-center text-purple-200 text-xs font-bold mr-3">
                                {{ $index + 1 }}
                            </div>
                            <span class="text-purple-100">{{ $user['name'] }}</span>
                        </div>
                        <div class="flex items-center">
                            <span class="text-purple-300 font-semibold">{{ $user['posts'] }}</span>
                            <span class="text-xs text-purple-400 ml-1">posts</span>
                        </div>
                    </li>
                @endforeach
            </ul>
            <div class="mt-4 text-right">
                <a href="{{ route('posts.index') }}" class="text-xs text-purple-300 hover:text-purple-100 flex items-center justify-end">
                    <span>Lihat Semua Postingan</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                </a>
            </div>
        </div>
    </div>
</div>
