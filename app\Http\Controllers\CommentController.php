<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use App\Models\Post;
use App\Notifications\CommentNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CommentController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Store a newly created comment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $postId
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, $postId)
    {
        $request->validate([
            'content' => 'required|string'
        ]);

        $post = Post::findOrFail($postId);
        $currentUser = Auth::user();

        $comment = new Comment([
            'content' => $request->content,
            'user_id' => Auth::id()
        ]);

        $post->comments()->save($comment);

        if ($post->user_id !== $currentUser->id) {
            $postOwner = $post->user;
            $postOwner->notify(new CommentNotification(
                $post->id,
                $post->title,
                $comment->id,
                Str::limit($comment->content, 50),
                $currentUser->name
            ));
        }

        return back()->with('success', 'Komentar berhasil ditambahkan');
    }

    /**
     * Delete a comment.
     *
     * @param  \App\Models\Comment  $comment
     * @return \Illuminate\Http\Response
     */
    public function destroy(Comment $comment)
    {
        // Check if user is allowed to delete this comment
        if (Auth::id() !== $comment->user_id && Auth::user()->role !== 'admin') {
            return back()->with('error', 'Anda tidak memiliki izin untuk menghapus komentar ini');
        }

        $comment->delete();

        return back()->with('success', 'Komentar berhasil dihapus');
    }
}
