@props(['tags'])

<ul class="grid-list-tags">
    @foreach ($tags as $tag)
        <li>
            <a href="{{ route('posts.by.tag', $tag['slug']) }}" class="card tag-btn">
                <div class="tag-img-container">
                    <img src="{{ $tag['img'] }}"
                        width="32"
                        height="32"
                        loading="lazy"
                        alt="{{ $tag['alt'] }}"
                        class="tag-img">
                </div>

                <p class="btn-text">{{ $tag['label'] }}</p>

                <data class="badge" value="{{ $tag['posts_count'] }}">{{ $tag['posts_count'] }}</data>
            </a>
        </li>
    @endforeach
</ul>

<style>
    .grid-list-tags {
        display: grid;
        gap: 12px;
        grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 500px) {
        .grid-list-tags {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (min-width: 768px) {
        .grid-list-tags {
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
        }
    }

    @media (min-width: 1024px) {
        .grid-list-tags {
            grid-template-columns: repeat(6, 1fr);
            gap: 20px;
        }
    }

    .tag-img-container {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;
    }

    .tag-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
    }

    .tag-btn {
        width: 100%;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        border-radius: var(--radius-16);
        transition: all 0.3s ease;
        background-color: var(--bg-oxford-blue);
    }

    @media (min-width: 768px) {
        .tag-btn {
            padding: 18px;
        }
    }

    .tag-btn:is(:hover, :focus-visible) {
        box-shadow: var(--shadow-2);
        background-image: linear-gradient(var(--bg-oxford-blue), var(--bg-oxford-blue)), var(--gradient-1);
        background-clip: padding-box, border-box;
        border-color: transparent;
        transform: translateY(-2px);
    }

    .btn-text {
        color: var(--text-white);
        font-size: 14px;
        font-weight: var(--weight-semiBold);
        flex-grow: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    @media (min-width: 768px) {
        .btn-text {
            font-size: var(--fontSize-6);
            white-space: normal;
        }
    }

    .badge {
        background-color: var(--bg-prussian-blue);
        color: var(--text-white);
        font-size: 12px;
        font-weight: var(--weight-semiBold);
        padding: 3px 8px;
        border-radius: 50px;
        min-width: 24px;
        text-align: center;
    }

    @media (min-width: 768px) {
        .badge {
            font-size: var(--fontSize-7);
            padding: 4px 12px;
        }
    }
</style>



