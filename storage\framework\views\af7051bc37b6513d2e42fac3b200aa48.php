<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name',
    'label',
    'placeholder' => '',
    'value' => '',
    'rows' => 5,
    'required' => false,
    'error' => false,
    'helper' => null,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name',
    'label',
    'placeholder' => '',
    'value' => '',
    'rows' => 5,
    'required' => false,
    'error' => false,
    'helper' => null,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="ckeditor-wrapper">
    <label for="<?php echo e($name); ?>" class="block text-sm font-medium text-gray-300">
        <?php echo e($label); ?>

        <?php if($required): ?>
            <span class="text-red-500">*</span>
        <?php endif; ?>
    </label>
    <div class="mt-1">
        <textarea
            id="<?php echo e($name); ?>"
            name="<?php echo e($name); ?>"
            rows="<?php echo e($rows); ?>"
            placeholder="<?php echo e($placeholder); ?>"
            <?php echo e($required ? 'required' : ''); ?>

            <?php echo e($attributes); ?>

            class="ckeditor-textarea"
        ><?php echo e($value); ?></textarea>
    </div>
    <?php if($error): ?>
        <p class="mt-1 text-sm text-red-500"><?php echo e($error); ?></p>
    <?php endif; ?>
    <?php if($helper): ?>
        <p class="mt-1 text-sm text-gray-400"><?php echo e($helper); ?></p>
    <?php endif; ?>
</div>

<?php if (! $__env->hasRenderedOnce('8054f553-f077-480a-bc69-badc998b054f')): $__env->markAsRenderedOnce('8054f553-f077-480a-bc69-badc998b054f'); ?>
    <?php $__env->startPush('scripts'); ?>
    <script src="https://cdn.ckeditor.com/4.22.1/standard/ckeditor.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Only target textareas with the ckeditor-textarea class
            const textareas = document.querySelectorAll('textarea.ckeditor-textarea');

            textareas.forEach(textarea => {
                if (textarea.id) {
                    CKEDITOR.replace(textarea.id, {
                        uiColor: '#374151',
                        height: 300,
                        removeButtons: 'Image,Flash,Table,Smiley,PageBreak,Iframe',
                        contentsCss: [
                            'body { color: #e5e7eb; background-color: #1f2937; font-family: sans-serif, Arial, Verdana, "Trebuchet MS"; font-size: 14px; }',
                            'h1, h2, h3, h4, h5, h6 { color: #e5e7eb; }',
                            'blockquote { border-left: 4px solid #6366f1; padding-left: 1em; margin-left: 0; font-style: italic; }',
                            'a { color: #93c5fd; text-decoration: underline; }',
                            'ul { list-style-type: disc; margin-left: 1.5em; padding-left: 1em; }',
                            'ol { list-style-type: decimal; margin-left: 1.5em; padding-left: 1em; }',
                            'li { margin-bottom: 0.5em; }'
                        ].join('')
                    });
                }
            });
        });
    </script>
    <?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/components/form/ckeditor-textarea.blade.php ENDPATH**/ ?>