@props(['user', 'profile'])

@push('styles')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
@endpush

<div class="relative group">
    <img id="avatar-preview" class="w-24 h-24 rounded-full border-4 border-gray-800"
        src="{{ $profile->avatar ? Storage::url($profile->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&background=6366f1&color=fff' }}"
        alt="{{ $user->name }}">

    <!-- Upload Icon -->
    <label for="avatar-upload"
        class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full opacity-0 group-hover:opacity-100 cursor-pointer transition-opacity">
        <i class="fas fa-camera text-white text-xl"></i>
    </label>
    <input type="file" id="avatar-upload" name="avatar" class="hidden" accept="image/jpeg,image/png,image/gif">
</div>

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const avatarUpload = document.getElementById('avatar-upload');
            const avatarPreview = document.getElementById('avatar-preview');
            let originalSrc = avatarPreview.src;

            avatarUpload.addEventListener('change', async (e) => {
                const file = e.target.files[0];
                if (!file) return;

                // Validasi tipe file
                const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'File harus berupa gambar (JPG, PNG, atau GIF).',
                        icon: 'error'
                    });
                    return;
                }

                // Validasi ukuran file (2MB)
                if (file.size > 2 * 1024 * 1024) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Ukuran file tidak boleh lebih dari 2MB.',
                        icon: 'error'
                    });
                    return;
                }

                // Preview dan upload gambar
                const reader = new FileReader();
                reader.onload = async (e) => {
                    try {
                        // Tampilkan loading state
                        Swal.fire({
                            title: 'Mengupload...',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        const formData = new FormData();
                        formData.append('avatar', file);
                        formData.append('_token', '{{ csrf_token() }}');
                        formData.append('_method', 'PUT');

                        const response = await fetch('{{ route('profile.update') }}', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });

                        const data = await response.json();

                        if (!response.ok) throw new Error(data.message || 'Terjadi kesalahan');

                        if (data.success) {
                            // Update preview dengan URL baru dari server
                            avatarPreview.src = data.data.avatar;
                            originalSrc = avatarPreview.src;

                            Swal.fire({
                                title: 'Berhasil!',
                                text: 'Foto profil berhasil diperbarui!',
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });
                        }
                    } catch (error) {
                        avatarPreview.src = originalSrc;

                        Swal.fire({
                            title: 'Error!',
                            text: error.message || 'Terjadi kesalahan saat mengupload foto profil.',
                            icon: 'error'
                        });
                    }
                };
                reader.readAsDataURL(file);
            });
        });
    </script>
@endpush

<style>
    [x-cloak] {
        display: none !important;
    }
</style>

