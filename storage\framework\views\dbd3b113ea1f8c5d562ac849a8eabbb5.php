<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'name' => '',
    'id' => null,
    'label' => '',
    'required' => false,
    'disabled' => false,
    'error' => null,
    'helper' => null,
    'accept' => 'image/*',
    'currentFile' => null,
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'name' => '',
    'id' => null,
    'label' => '',
    'required' => false,
    'disabled' => false,
    'error' => null,
    'helper' => null,
    'accept' => 'image/*',
    'currentFile' => null,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
    $inputId = $id ?? $name;
    $hasCurrentFile = !empty($currentFile);
?>

<div class="mb-4">
    <?php if($label): ?>
        <label for="<?php echo e($inputId); ?>" class="block mb-2 text-sm font-medium text-indigo-200">
            <?php echo e($label); ?>

            <?php if($required): ?>
                <span class="text-red-400">*</span>
            <?php endif; ?>
        </label>
    <?php endif; ?>

    <div class="flex flex-col items-center space-y-2">
        <!-- Upload Area (Hidden if has current file) -->
        <div id="<?php echo e($inputId); ?>-upload-area"
            class="w-full flex items-center justify-center <?php echo e($hasCurrentFile ? 'hidden' : ''); ?>">
            <label for="<?php echo e($inputId); ?>"
                class="w-full flex flex-col items-center px-4 py-6 bg-gray-700/50 text-indigo-100 hover:bg-gray-700 rounded-lg border-2 border-dashed border-gray-500 hover:border-indigo-500 cursor-pointer transition-all duration-200">
                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                    <svg class="w-10 h-10 mb-3 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12">
                        </path>
                    </svg>
                    <p class="mb-2 text-sm text-center"><span class="font-semibold">Klik untuk upload</span> atau drag
                        and drop</p>
                    <p class="text-xs text-gray-400"><?php echo e($helper ?? 'PNG, JPG atau GIF (Maks. 2MB)'); ?></p>
                </div>

                <input type="file" name="<?php echo e($name); ?>" id="<?php echo e($inputId); ?>" accept="<?php echo e($accept); ?>"
                    <?php echo e($required && !$hasCurrentFile ? 'required' : ''); ?> <?php echo e($disabled ? 'disabled' : ''); ?>

                    <?php echo e($attributes->merge(['class' => 'hidden'])); ?> />
            </label>
        </div>

        <!-- Preview Container -->
        <div id="<?php echo e($inputId); ?>-preview"
            class="preview-container <?php echo e($hasCurrentFile ? '' : 'hidden'); ?> mt-2 w-full">
            <div class="relative p-2 rounded-lg border border-gray-600 bg-gray-800">
                <?php if($hasCurrentFile): ?>
                    <img src="<?php echo e($currentFile); ?>" alt="Preview" class="h-40 mx-auto object-contain rounded"
                        id="<?php echo e($inputId); ?>-preview-image">
                    <!-- Add hidden input to track that we have a current file -->
                    <input type="hidden" name="has_current_<?php echo e($name); ?>" value="1">
                <?php else: ?>
                    <img src="#" alt="Preview" class="h-40 mx-auto object-contain rounded"
                        id="<?php echo e($inputId); ?>-preview-image">
                <?php endif; ?>
                <button type="button" id="<?php echo e($inputId); ?>-clear"
                    class="absolute top-2 right-2 p-1 bg-red-600 rounded-full text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
                <p class="text-xs text-center text-gray-400 mt-1.5 file-name">
                    <?php if($hasCurrentFile): ?>
                        <?php echo e(basename($currentFile)); ?>

                    <?php endif; ?>
                </p>
            </div>
        </div>
    </div>

    <?php if($error): ?>
        <p class="mt-1 text-sm text-red-500"><?php echo e($error); ?></p>
    <?php endif; ?>
</div>

<?php if (! $__env->hasRenderedOnce('31549a88-7dfa-4593-ae0e-85c546238b12')): $__env->markAsRenderedOnce('31549a88-7dfa-4593-ae0e-85c546238b12'); ?>
    <?php $__env->startPush('scripts'); ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Setup file input previews
                document.querySelectorAll('input[type="file"]').forEach(inputElement => {
                    const fileId = inputElement.id;
                    const uploadArea = document.getElementById(`${fileId}-upload-area`);
                    const preview = document.getElementById(`${fileId}-preview`);
                    const previewImg = document.getElementById(`${fileId}-preview-image`);
                    const clearBtn = document.getElementById(`${fileId}-clear`);
                    const fileNameDisplay = preview.querySelector('.file-name');

                    if (preview && previewImg && clearBtn) {
                        inputElement.addEventListener('change', function(e) {
                            if (this.files && this.files[0]) {
                                const file = this.files[0];
                                const reader = new FileReader();

                                reader.onload = function(e) {
                                    previewImg.src = e.target.result;
                                    preview.classList.remove('hidden');
                                    uploadArea.classList.add('hidden');
                                    fileNameDisplay.textContent = file.name;
                                }

                                reader.readAsDataURL(file);
                            }
                        });

                        clearBtn.addEventListener('click', function() {
                            inputElement.value = '';
                            preview.classList.add('hidden');
                            uploadArea.classList.remove('hidden');
                            previewImg.src = '#';
                            fileNameDisplay.textContent = '';

                            // Clear the current file flag if it exists
                            const hiddenInput = document.querySelector(
                                `input[name="has_current_${inputElement.name}"]`);
                            if (hiddenInput) {
                                hiddenInput.value = '0';
                            }
                        });
                    }
                });
            });
        </script>
    <?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/components/form/file-upload.blade.php ENDPATH**/ ?>