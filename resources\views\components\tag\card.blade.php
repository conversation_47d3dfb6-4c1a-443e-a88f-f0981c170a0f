@props(['tag'])

<div
    class="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700 hover:border-indigo-500 transition-all duration-300 flex flex-col">
    @if ($tag->image)
        <div class="h-32 overflow-hidden">
            <img src="{{ Storage::url($tag->image) }}" alt="{{ $tag->title }}"
                class="w-full h-full object-cover transition-transform duration-500 transform hover:scale-110">
        </div>
    @else
        <div class="h-32 bg-gradient-to-br from-indigo-900 to-purple-900 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-indigo-300" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                    d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
        </div>
    @endif

    <div class="p-4 flex-1 flex flex-col">
        <span class="text-xs text-gray-400 mb-2">Tag</span>
        <h3 class="text-lg font-bold text-white mb-2">{{ $tag->title }}</h3>

        <div class="text-sm text-gray-400 mb-4 flex-1">
            <p>{{ Str::limit($tag->description ?? 'Tag untuk mengkategorikan konten berdasarkan ' . $tag->title, 100) }}
            </p>
        </div>

        <div class="flex justify-between items-center pt-3 border-t border-gray-700 mt-auto">
            <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-900 text-indigo-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                </svg>
                {{ $tag->posts_count ?? 0 }} Postingan
            </span>

            {{ $slot }}
        </div>
    </div>
</div>
