<?php

use App\Http\Controllers\CategoryController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PostController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\OrganizationStructureController;
use App\Http\Controllers\ReplyCommentController;
use Illuminate\Support\Facades\Route;

require __DIR__ . '/auth.php';
require __DIR__ . '/app.php';

Route::get('/storage-link', function () {
    $targetFolder = base_path() . '/storage/app/public';
    $linkFolder =  $_SERVER['DOCUMENT_ROOT'] . '/storage';

    if (!file_exists($linkFolder)) {
        symlink($targetFolder, $linkFolder);
        return redirect()->back()->with('success', 'Penyimpanan di server sudah diaktifkan!');
    } else {
        return redirect()->back()->with('error', 'Penyimpanan di server telah tersedia!');
    }
})->name('storage-link');

Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/all-posts', [PostController::class, 'allPosts'])->name('posts.all');

Route::post('/posts/{post}/comments', [CommentController::class, 'store'])->name('comments.store');
Route::delete('/comments/{comment}', [CommentController::class, 'destroy'])->name('comments.destroy');

Route::post('/comments/{comment}/replies', [ReplyCommentController::class, 'store'])->name('replies.store');
Route::delete('/replies/{reply}', [ReplyCommentController::class, 'destroy'])->name('replies.destroy');

Route::get('/tag/{slug}', [TagController::class, 'show'])->name('posts.by.tag');
Route::get('/categori/{slug}', [CategoryController::class, 'show'])->name('categories.show');
Route::get('/struktur-organisasi', [OrganizationStructureController::class, 'frontPage'])->name('organization.public.index');
Route::get('/sturcture-detail/{id}', [OrganizationStructureController::class, 'show'])->name('organization.show');

Route::get('/{slug}', [PostController::class, 'show'])->name('posts.show');

Route::middleware('auth')->group(function () {
    Route::post('/posts/{post}/approve', [PostController::class, 'approvePost'])->name('posts.approve');
    Route::post('/posts/{post}/reject', [PostController::class, 'rejectPost'])->name('posts.reject');
    Route::post('/posts/{post}/like', [PostController::class, 'like'])->name('posts.like');
    Route::post('/posts/{post}/dislike', [PostController::class, 'dislike'])->name('posts.dislike');
    Route::delete('/posts/{post}/unlike', [PostController::class, 'unlike'])->name('posts.unlike');
    Route::delete('/posts/{post}/undislike', [PostController::class, 'undislike'])->name('posts.undislike');
});
