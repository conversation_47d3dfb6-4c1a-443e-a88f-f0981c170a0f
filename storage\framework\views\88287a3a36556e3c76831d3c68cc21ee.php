<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['title' => '', 'action' => null, 'padding' => 'p-6']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['title' => '', 'action' => null, 'padding' => 'p-6']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div <?php echo e($attributes->merge(['class' => 'bg-gray-800 shadow-xl rounded-lg overflow-hidden mb-6'])); ?>>
    <?php if($title || $action): ?>
        <div class="px-6 py-4 bg-gradient-to-r from-indigo-800 to-purple-800 flex items-center justify-between">
            <h2 class="text-xl font-bold text-white"><?php echo e($title); ?></h2>
            <?php if($action): ?>
                <div>
                    <?php echo e($action); ?>

                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <div class="<?php echo e($padding); ?>">
        <?php echo e($slot); ?>

    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/components/layout/section.blade.php ENDPATH**/ ?>