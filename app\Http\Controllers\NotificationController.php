<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Post;
use App\Models\Comment;
use Illuminate\Support\Str;

class NotificationController extends Controller
{
    public function markAsRead(Notification $notification)
    {
        if ($notification->notifiable_id == Auth::id()) {
            $notification->read_at = now();
            $notification->save();
        }

        return redirect()->back();
    }

    public function index()
    {
        $user = Auth::user();

        $readNotifications = Notification::where('notifiable_id', $user->id)
            ->where('notifiable_type', 'App\\Models\\User')
            ->whereNotNull('read_at')
            ->get();

        $unreadNotifications = Notification::where('notifiable_id', $user->id)
            ->where('notifiable_type', 'App\\Models\\User')
            ->whereNull('read_at')
            ->get();

        $notifications = Notification::where('notifiable_id', $user->id)
            ->where('notifiable_type', 'App\\Models\\User')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // dd($notifications);

        return view('app.notifications.index', [
            'notifications' => $notifications,
            'readNotifications' => $readNotifications,
            'unreadNotifications' => $unreadNotifications
        ]);
    }

    public function show(Notification $notification)
    {
        $user = Auth::user();

        if ($notification->notifiable_id != $user->id || $notification->notifiable_type != 'App\\Models\\User') {
            return redirect()->route('notifications.index')->with('error', 'Notifikasi tidak ditemukan atau Anda tidak memiliki akses');
        }

        if ($notification->read_at === null) {
            $notification->read_at = now();
            $notification->save();
        }

        $subject = null;
        $data = $notification->data;

        if (isset($data['post_id'])) {
            $subject = Post::find($data['post_id']);
        } elseif (isset($data['comment_id'])) {
            $subject = Comment::find($data['comment_id']);
        } elseif (isset($data['user_id'])) {
            $subject = User::find($data['user_id']);
        }

        return view('app.notifications.show', [
            'notification' => $notification,
            'subject' => $subject
        ]);
    }
}
