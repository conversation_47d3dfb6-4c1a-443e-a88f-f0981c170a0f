@props([
    'name',
    'label',
    'placeholder' => '',
    'value' => '',
    'rows' => 5,
    'required' => false,
    'error' => false,
    'helper' => null,
])

<div class="ckeditor-wrapper">
    <label for="{{ $name }}" class="block text-sm font-medium text-gray-300">
        {{ $label }}
        @if ($required)
            <span class="text-red-500">*</span>
        @endif
    </label>
    <div class="mt-1">
        <textarea
            id="{{ $name }}"
            name="{{ $name }}"
            rows="{{ $rows }}"
            placeholder="{{ $placeholder }}"
            {{ $required ? 'required' : '' }}
            {{ $attributes }}
            class="ckeditor-textarea"
        >{{ $value }}</textarea>
    </div>
    @if ($error)
        <p class="mt-1 text-sm text-red-500">{{ $error }}</p>
    @endif
    @if ($helper)
        <p class="mt-1 text-sm text-gray-400">{{ $helper }}</p>
    @endif
</div>

@once
    @push('scripts')
    <script src="https://cdn.ckeditor.com/4.22.1/standard/ckeditor.js"></script>
    {{-- <script src="https://cdn.ckeditor.com/4.25.1-lts/standard/ckeditor.js"></script> --}}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Only target textareas with the ckeditor-textarea class
            const textareas = document.querySelectorAll('textarea.ckeditor-textarea');

            textareas.forEach(textarea => {
                if (textarea.id) {
                    CKEDITOR.replace(textarea.id, {
                        uiColor: '#374151',
                        height: 300,
                        removeButtons: 'Image,Flash,Table,Smiley,PageBreak,Iframe',
                        contentsCss: [
                            'body { color: #e5e7eb; background-color: #1f2937; font-family: sans-serif, Arial, Verdana, "Trebuchet MS"; font-size: 14px; }',
                            'h1, h2, h3, h4, h5, h6 { color: #e5e7eb; }',
                            'blockquote { border-left: 4px solid #6366f1; padding-left: 1em; margin-left: 0; font-style: italic; }',
                            'a { color: #93c5fd; text-decoration: underline; }',
                            'ul { list-style-type: disc; margin-left: 1.5em; padding-left: 1em; }',
                            'ol { list-style-type: decimal; margin-left: 1.5em; padding-left: 1em; }',
                            'li { margin-bottom: 0.5em; }'
                        ].join('')
                    });
                }
            });
        });
    </script>
    @endpush
@endonce
