@props(['items'])

<ul class="feature-list">
    @foreach ($items as $item)
        <li>
            <div class="card feature-card">
                <figure class="card-banner img-holder" style="--width: 1602; --height: 903">
                    <a href="{{ route('posts.show', $item['slug']) }}">
                        <img src="{{ $item['img'] }}" width="1602" height="903" loading="lazy"
                             alt="{{ $item['alt'] }}" class="img-cover" />
                    </a>
                </figure>
                <div class="card-content">
                    <div class="card-wrapper">
                        <div class="card-tag">
                            @foreach ($item['tags'] as $tag)
                                <a href="{{ route('posts.by.tag', $tag['slug']) }}">
                                    <span class="span">{{ $tag['title'] }}</span>
                                </a>
                            @endforeach
                        </div>
                        <div class="wrapper" style="display: flex; gap: 15px;">
                            <div style="display: flex; align-items: center; gap: 4px;">
                                <ion-icon name="eye-outline" aria-hidden="true"></ion-icon>
                                <span class="span">{{ number_format($item['views']) }}</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 4px;">
                                <ion-icon name="thumbs-up-outline" aria-hidden="true"></ion-icon>
                                <span class="span">{{ number_format($item['likes']) }}</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 4px;">
                                <ion-icon name="thumbs-down-outline" aria-hidden="true"></ion-icon>
                                <span class="span">{{ number_format($item['dislikes']) }}</span>
                            </div>
                        </div>
                    </div>
                    <h3 class="headline headline-3">
                        <a href="{{ route('posts.show', $item['slug']) }}" class="card-title hover-2">
                            {{ $item['title'] }}
                        </a>
                    </h3>
                    <div class="card-wrapper">
                        <div class="profile-card">
                            <img src="{{ $item['author_img'] }}" width="48" height="48" loading="lazy"
                                alt="{{ $item['author'] }}" class="profile-banner"
                                style="border-radius: 50%; object-fit: cover; aspect-ratio: 1/1; overflow: hidden;" />
                            <div>
                                <p class="card-title">{{ $item['author'] }}</p>
                                <p class="card-subtitle">{{ $item['date'] }}</p>
                            </div>
                        </div>
                        <a href="{{ route('posts.show', $item['slug']) }}" class="card-btn">Read more</a>
                    </div>
                </div>
            </div>
        </li>
    @endforeach
</ul>

