<a href="{{ route('notifications.show', $notification->id) }}"
    class="block p-4 {{ $notification->read_at ? 'bg-gray-900/80' : 'bg-indigo-900/80 border-l-4 border-indigo-500' }} rounded-lg hover:bg-indigo-800/70 transition-all duration-300 transform hover:-translate-y-1 shadow-md relative overflow-hidden">

    @if (isset($showDebug) && $showDebug)
        <!-- Debug info - UUID lengkap -->
        <div class="absolute top-1 right-1 text-xs text-gray-500">UUID: {{ $notification->id }}</div>
    @endif

    @if (!$notification->read_at)
        <div class="absolute top-0 right-0 w-6 h-6">
            <div
                class="bg-indigo-500 text-white transform rotate-45 text-center text-[10px] py-0.5 absolute top-[0px] right-[-12px] w-[50px]">
                Baru</div>
        </div>
    @endif

    <div class="flex items-center">
        <div
            class="flex-shrink-0 w-10 h-10 rounded-full {{ $notification->read_at ? 'bg-gray-800' : 'bg-indigo-800' }} flex items-center justify-center text-2xl mr-3">
            {{ $notification->data['icon'] }}
        </div>
        <div class="flex-1">
            <div class="text-indigo-100 font-medium flex items-center">
                {{ $notification->data['message'] }}
                @if (!$notification->read_at)
                    <span class="ml-2 h-2 w-2 bg-indigo-400 rounded-full"></span>
                @endif
            </div>
            <div class="text-xs text-indigo-300 mt-1 flex items-center justify-between">
                <span>{{ $notification->created_at->diffForHumans() }}</span>

                @if (isset($notification->data['subject_type']))
                    <span
                        class="px-2 py-0.5 rounded text-xs {{ $notification->read_at ? 'bg-gray-700 text-gray-300' : 'bg-indigo-700 text-indigo-200' }}">
                        {{ substr(strrchr($notification->data['subject_type'] ?? '', '\\'), 1) ?? 'Umum' }}
                    </span>
                @endif
            </div>
        </div>
    </div>
</a>
