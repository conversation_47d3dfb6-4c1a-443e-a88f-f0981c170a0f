<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['items']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['items']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<ul class="slider-list" data-slider-container>
    <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li class="slider-item">
            <a href="<?php echo e(route('organization.show', $item['id'])); ?>" class="slider-card">
                <figure class="slider-banner img-holder" style="--width: 507; --height: 618">
                    <img src="<?php echo e(isset($item['img']) && strpos($item['img'], 'http') === 0 ? $item['img'] : asset($item['img'])); ?>"
                        width="507" height="618" loading="lazy"
                        alt="<?php echo e($item['alt'] ?? $item['title']); ?>" class="img-cover" />
                </figure>
                <div class="slider-content">
                    <span class="slider-title"><?php echo e($item['title']); ?></span>
                    <p class="slider-subtitle"><?php echo e($item['subtitle']); ?></p>
                </div>
            </a>
        </li>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</ul>
<?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/components/slider/BidangSlider.blade.php ENDPATH**/ ?>