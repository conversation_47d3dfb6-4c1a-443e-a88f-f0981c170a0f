@props(['title', 'value', 'icon' => null, 'color' => 'indigo', 'percentage' => null])

@php
    $colors = [
        'indigo' => [
            'bg' => 'bg-indigo-900/40',
            'border' => 'border-indigo-700',
            'text' => 'text-indigo-300',
            'icon' => 'text-indigo-400',
            'value' => 'text-indigo-100',
            'iconBg' => 'bg-indigo-500/20',
        ],
        'purple' => [
            'bg' => 'bg-purple-900/40',
            'border' => 'border-purple-700',
            'text' => 'text-purple-300',
            'icon' => 'text-purple-400',
            'value' => 'text-purple-100',
            'iconBg' => 'bg-purple-500/20',
        ],
        'pink' => [
            'bg' => 'bg-pink-900/40',
            'border' => 'border-pink-700',
            'text' => 'text-pink-300',
            'icon' => 'text-pink-400',
            'value' => 'text-pink-100',
            'iconBg' => 'bg-pink-500/20',
        ],
    ];

    $currentColor = $colors[$color] ?? $colors['indigo'];

    $icons = [
        'posts' =>
            '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M19 20a2 2 0 002-2V8a2 2 0 00-2-2h-1M9 12h.01M12 12h.01M15 12h.01M9 16h.01M12 16h.01M15 16h.01" /></svg>',
        'views' =>
            '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>',
        'comments' =>
            '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" /></svg>',
        'users' =>
            '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" /></svg>',
        'default' =>
            '<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" /></svg>',
    ];

    $iconKey = 'default';
    if ($icon) {
        $iconKey = $icon;
    } else {
        if (stripos($title, 'postingan') !== false) {
            $iconKey = 'posts';
        } elseif (stripos($title, 'tonton') !== false) {
            $iconKey = 'views';
        } elseif (stripos($title, 'komen') !== false) {
            $iconKey = 'comments';
        } elseif (stripos($title, 'user') !== false) {
            $iconKey = 'users';
        }
    }

    $iconSvg = $icons[$iconKey];

    if ($percentage === null) {
        if (stripos($title, 'postingan') !== false) {
            $percentage = $postStats['posts_percentage'] ?? 0;
        } elseif (stripos($title, 'tonton') !== false) {
            $percentage = $postStats['views_percentage'] ?? 0;
        } elseif (stripos($title, 'komen') !== false) {
            $percentage = $postStats['comments_percentage'] ?? 0;
        } else {
            $percentage = 0;
        }
    }

    $percentageColor = $percentage >= 0 ? 'bg-green-800/30 text-green-400' : 'bg-red-800/30 text-red-400';
    $percentageIcon = $percentage >= 0
        ? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />'
        : '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />';
    $percentageValue = abs($percentage);
@endphp

<div
    {{ $attributes->merge(['class' => "p-6 {$currentColor['bg']} rounded-xl shadow-lg border {$currentColor['border']} hover:translate-y-[-5px] transition duration-300 ease-in-out"]) }}>
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold {{ $currentColor['text'] }}">{{ $title }}</h3>
        <div
            class="w-12 h-12 rounded-lg {{ $currentColor['iconBg'] }} flex items-center justify-center {{ $currentColor['icon'] }}">
            {!! $iconSvg !!}
        </div>
    </div>
    <div class="mt-2 flex items-end">
        <p class="text-4xl font-bold {{ $currentColor['value'] }}">{{ $value }}</p>

        <div class="ml-4 mb-1.5 px-2 py-1 {{ $percentageColor }} rounded text-xs flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                {!! $percentageIcon !!}
            </svg>
            {{ $percentageValue }}%
        </div>
    </div>
    <p class="text-xs {{ $currentColor['text'] }} mt-1 opacity-70">Dibandingkan bulan lalu</p>
</div>
