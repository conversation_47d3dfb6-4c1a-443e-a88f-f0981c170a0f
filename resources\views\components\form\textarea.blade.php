@props([
    'name' => '',
    'id' => null,
    'label' => '',
    'placeholder' => '',
    'value' => '',
    'required' => false,
    'disabled' => false,
    'error' => null,
    'helper' => null,
    'rows' => 4,
])

@php
    $inputId = $id ?? $name;
@endphp

<div class="mb-4">
    @if ($label)
        <label for="{{ $inputId }}" class="block mb-2 text-sm font-medium text-indigo-200">
            {{ $label }}
            @if ($required)
                <span class="text-red-400">*</span>
            @endif
        </label>
    @endif

    <div class="relative">
        <textarea name="{{ $name }}" id="{{ $inputId }}" rows="{{ $rows }}" placeholder="{{ $placeholder }}"
            {{ $required ? 'required' : '' }} {{ $disabled ? 'disabled' : '' }}
            {{ $attributes->merge([
                'class' =>
                    'block w-full px-4 py-2.5 bg-gray-700 border ' .
                    ($error
                        ? 'border-red-500 focus:ring-red-500'
                        : 'border-gray-600 focus:border-indigo-500 focus:ring-indigo-500') .
                    ' rounded-lg text-sm text-indigo-100 focus:outline-none focus:ring-1 shadow-sm',
            ]) }}>{{ $value }}</textarea>
    </div>

    @if ($error)
        <p class="mt-1 text-sm text-red-500">{{ $error }}</p>
    @elseif ($helper)
        <p class="mt-1 text-sm text-gray-400">{{ $helper }}</p>
    @endif
</div>
