<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Post;

class CommentNotification extends Notification
{
    use Queueable;

    protected $postId;
    protected $postTitle;
    protected $commentId;
    protected $commentContent;
    protected $commentAuthor;
    protected $postSlug;

    /**
     * Create a new notification instance.
     */
    public function __construct($postId, $postTitle, $commentId, $commentContent, $commentAuthor, $postSlug = null)
    {
        $this->postId = $postId;
        $this->postTitle = $postTitle;
        $this->commentId = $commentId;
        $this->commentContent = $commentContent;
        $this->commentAuthor = $commentAuthor;
        $this->postSlug = $postSlug;

        // If slug is not provided, try to get it from the post
        if ($this->postSlug === null) {
            $post = Post::find($postId);
            if ($post) {
                $this->postSlug = $post->slug;
            }
        }
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->line('Ada komentar baru pada postingan Anda.')
            ->action('Lihat Komentar', route('posts.show', $this->postId))
            ->line('Terima kasih telah menggunakan aplikasi kami!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'icon' => '💬',
            'message' => "{$this->commentAuthor} mengomentari postingan Anda: \"{$this->postTitle}\"",
            'href' => $this->postSlug ? route('posts.show', $this->postSlug) : route('posts.show', $this->postId),
            'post_id' => $this->postId,
            'comment_id' => $this->commentId,
            'comment_content' => $this->commentContent,
            'comment_author' => $this->commentAuthor
        ];
    }
}
