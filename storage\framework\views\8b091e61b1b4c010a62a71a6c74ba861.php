<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['post']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['post']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div
    class="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700 hover:border-indigo-500 transition-all duration-300 hover:shadow-xl hover:shadow-indigo-900/10 hover:-translate-y-1">
    <?php if($post->image): ?>
        <div class="h-48 overflow-hidden">
            <img src="<?php echo e(Storage::url($post->image)); ?>" alt="<?php echo e($post->title); ?>"
                class="w-full h-full object-cover transition-transform duration-500 transform hover:scale-110">
        </div>
    <?php endif; ?>

    <div class="p-5">
        <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
                <?php if($post->category): ?>
                    <span class="px-2 py-1 text-xs font-medium bg-indigo-900/70 text-indigo-300 rounded-md">
                        <?php echo e($post->category->title); ?>

                    </span>
                <?php endif; ?>

                <?php if(!$post->is_published): ?>
                    <span
                        class="px-2 py-1 text-xs font-medium bg-yellow-900/70 text-yellow-300 rounded-md flex items-center gap-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Pending
                    </span>
                <?php else: ?>
                    <span
                        class="px-2 py-1 text-xs font-medium bg-green-900/70 text-green-300 rounded-md flex items-center gap-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Published
                    </span>
                <?php endif; ?>

                <span class="text-xs text-gray-400"><?php echo e($post->created_at->diffForHumans()); ?></span>
            </div>

            <div class="flex items-center space-x-1 text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                </svg>
                <span class="text-xs"><?php echo e($post->comments->count()); ?></span>
            </div>
        </div>

        <h3 class="text-xl font-bold text-white mb-2 line-clamp-2"><?php echo e($post->title); ?></h3>

        <p class="text-gray-400 text-sm mb-4 line-clamp-3"><?php echo e($post->description); ?></p>

        <?php if($post->tags && $post->tags->count() > 0): ?>
            <div class="mb-4 flex flex-wrap gap-1">
                <?php $__currentLoopData = $post->tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <span class="inline-block px-2 py-0.5 text-xs bg-gray-700 text-gray-300 rounded">
                        #<?php echo e($tag->title); ?>

                    </span>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        <?php endif; ?>

        <div class="flex items-center justify-between pt-3 border-t border-gray-700">
            <div class="flex items-center space-x-2">
                <div class="h-8 w-8 rounded-full overflow-hidden flex items-center justify-center">
                    <?php if($post->user && $post->user->avatar): ?>
                        <img src="<?php echo e(Storage::url($post->user->avatar)); ?>" alt="<?php echo e($post->user->name); ?>"
                            class="w-full h-full object-cover">
                    <?php else: ?>
                        <div class="h-full w-full bg-indigo-800 flex items-center justify-center">
                            <span
                                class="text-white text-xs font-medium"><?php echo e(strtoupper(substr($post->user->name ?? 'U', 0, 1))); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
                <span class="text-sm font-medium text-indigo-300"><?php echo e($post->user->name ?? 'Anonymous'); ?></span>
            </div>

            <?php echo e($slot); ?>

        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/components/post/card.blade.php ENDPATH**/ ?>