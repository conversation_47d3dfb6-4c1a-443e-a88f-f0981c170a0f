@props(['category'])

<div
    class="bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700 hover:border-indigo-500 transition-all duration-300 flex flex-col">
    @if ($category->banner)
        <div class="h-40 overflow-hidden">
            <img src="{{ Storage::url($category->banner) }}" alt="{{ $category->title }}"
                class="w-full h-full object-cover transition-transform duration-500 transform hover:scale-110">
        </div>
    @else
        <div class="h-40 bg-gradient-to-r from-indigo-800 to-purple-800 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-indigo-200" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
        </div>
    @endif

    <div class="p-4 flex-1 flex flex-col">
        <span class="text-xs text-gray-400 mb-2">Kategori</span>
        <h3 class="text-lg font-bold text-white mb-2">{{ $category->title }}</h3>

        <div class="text-sm text-gray-400 mb-4 flex-1">
            <p>{{ Str::limit($category->description ?? 'Kategori untuk pengelompokan konten ' . $category->title, 100) }}
            </p>
        </div>

        <div class="flex justify-between items-center pt-3 border-t border-gray-700 mt-auto">
            <span
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-900 text-indigo-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                </svg>
                {{ $category->posts_count ?? ($category->posts->count() ?? 0) }} Postingan
            </span>

            {{ $slot }}
        </div>
    </div>
</div>
