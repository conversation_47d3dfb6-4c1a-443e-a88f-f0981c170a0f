<?php

namespace App\Http\Controllers;

use App\Notifications\ProfileCompletionSuccessNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Storage;

class ProfileController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $profile = $user->profile;

        if (!$profile) {
            return redirect()
                ->route('profile.create')
                ->with('toast', [
                    'title' => 'Perhatian!',
                    'message' => 'Silakan lengkapi profil Anda terlebih dahulu.',
                    'icon' => 'warning'
                ]);
        }

        return view('app.profile.index', compact('user', 'profile'));
    }

    public function create()
    {
        if (Auth::user()->profile) {
            return redirect()->route('profile.index');
        }
        return view('app.profile.create');
    }

    public function store(Request $request)
    {
        $messages = [
            'avatar.required' => 'Foto profil wajib diunggah',
            'avatar.image' => 'File harus berupa gambar',
            'avatar.mimes' => 'Format gambar harus jpeg, png, jpg, atau gif',
            'avatar.max' => 'Ukuran gambar tidak boleh lebih dari 2MB',
            'address.required' => 'Alamat wajib diisi',
            'address.string' => 'Alamat harus berupa teks',
            'address.max' => 'Alamat tidak boleh lebih dari :max karakter',
            'province.required' => 'Provinsi wajib diisi',
            'province.string' => 'Provinsi harus berupa teks',
            'province.max' => 'Provinsi tidak boleh lebih dari :max karakter',
            'city.required' => 'Kota wajib diisi',
            'city.string' => 'Kota harus berupa teks',
            'city.max' => 'Kota tidak boleh lebih dari :max karakter',
            'phone.required' => 'Nomor telepon wajib diisi',
            'phone.string' => 'Nomor telepon harus berupa teks',
            'phone.max' => 'Nomor telepon tidak boleh lebih dari :max karakter',
            'hobbies.required' => 'Hobi wajib diisi',
            'hobbies.string' => 'Hobi harus berupa teks',
            'hobbies.max' => 'Hobi tidak boleh lebih dari :max karakter',
            'bio.required' => 'Bio wajib diisi',
            'bio.string' => 'Bio harus berupa teks',
            'bio.max' => 'Bio tidak boleh lebih dari :max karakter',
        ];

        try {
            $validated = $request->validate([
                'avatar' => ['required', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
                'address' => ['required', 'string', 'max:255'],
                'province' => ['required', 'string', 'max:255'],
                'city' => ['required', 'string', 'max:255'],
                'phone' => ['required', 'string', 'max:20'],
                'hobbies' => ['required', 'string', 'max:255'],
                'bio' => ['required', 'string', 'max:1000'],
            ], $messages);

            if ($request->hasFile('avatar')) {
                $avatarPath = $request->file('avatar')->store('avatars', 'public');
                $validated['avatar'] = $avatarPath;
            }

            $request->user()->profile()->create($validated);

            $request->user()->notify(new ProfileCompletionSuccessNotification());

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Profil berhasil disimpan!',
                    'redirect' => route('app.dashboard'),
                    'title' => 'Berhasil!',
                    'icon' => 'success'
                ]);
            }

            return redirect()
                ->route('app.dashboard')
                ->with('success', 'Profil berhasil disimpan!')
                ->with('toast', [
                    'title' => 'Berhasil!',
                    'message' => 'Profil berhasil disimpan!',
                    'icon' => 'success'
                ]);
        } catch (ValidationException $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi gagal',
                    'errors' => $e->errors(),
                    'title' => 'Gagal!',
                    'icon' => 'error'
                ], 422);
            }

            throw $e;
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat menyimpan profil.',
                    'title' => 'Error!',
                    'icon' => 'error'
                ], 500);
            }

            return back()
                ->withInput()
                ->with('error', 'Terjadi kesalahan saat menyimpan profil.')
                ->with('toast', [
                    'title' => 'Error!',
                    'message' => 'Terjadi kesalahan saat menyimpan profil.',
                    'icon' => 'error'
                ]);
        }
    }

    public function update(Request $request)
    {
        if ($request->hasFile('avatar')) {
            try {
                $validated = $request->validate([
                    'avatar' => [
                        'required',
                        'image',
                        'mimes:jpeg,png,gif',
                        'max:2048', // 2MB
                    ]
                ], [
                    'avatar.required' => 'File gambar wajib diunggah',
                    'avatar.image' => 'File harus berupa gambar',
                    'avatar.mimes' => 'Format gambar harus jpeg, png, atau gif',
                    'avatar.max' => 'Ukuran gambar tidak boleh lebih dari 2MB',
                ]);

                $profile = $request->user()->profile;

                // Hapus avatar lama jika ada
                if ($profile->avatar) {
                    Storage::disk('public')->delete($profile->avatar);
                }

                // Simpan avatar baru
                $avatarPath = $request->file('avatar')->store('avatars', 'public');
                $profile->update(['avatar' => $avatarPath]);

                return response()->json([
                    'success' => true,
                    'message' => 'Foto profil berhasil diperbarui!',
                    'data' => [
                        'avatar' => Storage::url($avatarPath)
                    ]
                ]);
            } catch (ValidationException $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi gagal',
                    'errors' => $e->errors()
                ], 422);
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat memperbarui foto profil.'
                ], 500);
            }
        }

        $messages = [
            'avatar.image' => 'File harus berupa gambar',
            'avatar.mimes' => 'Format gambar harus jpeg, png, jpg, atau gif',
            'avatar.max' => 'Ukuran gambar tidak boleh lebih dari 2MB',
            'address.required' => 'Alamat wajib diisi',
            'address.string' => 'Alamat harus berupa teks',
            'address.max' => 'Alamat tidak boleh lebih dari :max karakter',
            'province.required' => 'Provinsi wajib diisi',
            'province.string' => 'Provinsi harus berupa teks',
            'province.max' => 'Provinsi tidak boleh lebih dari :max karakter',
            'city.required' => 'Kota wajib diisi',
            'city.string' => 'Kota harus berupa teks',
            'city.max' => 'Kota tidak boleh lebih dari :max karakter',
            'phone.required' => 'Nomor telepon wajib diisi',
            'phone.string' => 'Nomor telepon harus berupa teks',
            'phone.max' => 'Nomor telepon tidak boleh lebih dari :max karakter',
            'hobbies.required' => 'Hobi wajib diisi',
            'hobbies.string' => 'Hobi harus berupa teks',
            'hobbies.max' => 'Hobi tidak boleh lebih dari :max karakter',
            'bio.required' => 'Bio wajib diisi',
            'bio.string' => 'Bio harus berupa teks',
            'bio.max' => 'Bio tidak boleh lebih dari :max karakter',
        ];

        try {
            $rules = [
                'address' => ['required', 'string', 'max:255'],
                'province' => ['required', 'string', 'max:255'],
                'city' => ['required', 'string', 'max:255'],
                'phone' => ['required', 'string', 'max:20'],
                'hobbies' => ['required', 'string', 'max:255'],
                'bio' => ['required', 'string', 'max:1000'],
            ];

            // Add avatar validation if it's being updated (but make it optional)
            if ($request->hasFile('avatar')) {
                $rules['avatar'] = ['image', 'mimes:jpeg,png,jpg,gif', 'max:2048'];
            }

            $validated = $request->validate($rules, $messages);

            $profile = $request->user()->profile;

            // Handle avatar upload if present
            if ($request->hasFile('avatar')) {
                // Delete old avatar if it exists
                if ($profile->avatar) {
                    Storage::disk('public')->delete($profile->avatar);
                }

                $avatarPath = $request->file('avatar')->store('avatars', 'public');
                $validated['avatar'] = $avatarPath;
            }

            $profile->update($validated);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Profil berhasil diperbarui!',
                    'data' => $profile
                ]);
            }

            return redirect()->route('profile.index')
                ->with('success', 'Profil berhasil diperbarui!')
                ->with('toast', [
                    'title' => 'Berhasil!',
                    'message' => 'Profil berhasil diperbarui!',
                    'icon' => 'success'
                ]);
        } catch (ValidationException $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi gagal',
                    'errors' => $e->errors()
                ], 422);
            }
            throw $e;
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat memperbarui profil: ' . $e->getMessage()
                ], 500);
            }
            throw $e;
        }
    }

    public function updateField(Request $request)
    {
        $field = array_keys($request->except(['_token', '_method']))[0] ?? null;

        if (!$field) {
            return response()->json([
                'success' => false,
                'message' => 'Tidak ada field yang diperbarui'
            ], 400);
        }

        $allowedFields = [
            'address',
            'province',
            'city',
            'phone',
            'hobbies',
            'bio'
        ];

        if (!in_array($field, $allowedFields)) {
            return response()->json([
                'success' => false,
                'message' => 'Field tidak diizinkan untuk diperbarui'
            ], 400);
        }

        try {
            $profile = $request->user()->profile;
            $profile->update([$field => $request->input($field)]);

            return response()->json([
                'success' => true,
                'message' => 'Field berhasil diperbarui',
                'data' => [
                    $field => $profile->$field
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui field: ' . $e->getMessage()
            ], 500);
        }
    }
}
