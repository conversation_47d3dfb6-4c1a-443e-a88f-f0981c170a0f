<?php $__env->startSection('title', $member->name . ' - Struktur Organisasi'); ?>

<?php $__env->startSection('content'); ?>
    <style>
        .member-detail {
            padding-top: 90px;
        }

        .member-header {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
            margin-bottom: 50px;
        }

        @media (min-width: 768px) {
            .member-header {
                grid-template-columns: 300px 1fr;
                align-items: start;
            }
        }

        .member-banner {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .member-social {
            position: absolute;
            bottom: 20px;
            left: 20px;
            display: flex;
            gap: 10px;
        }

        .social-link {
            width: 40px;
            height: 40px;
            background-color: var(--bg-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            color: var(--foreground-primary);
            font-size: 1.5rem;
            transition: 0.25s ease;
        }

        .social-link:hover {
            background-color: var(--accent);
            color: var(--white);
        }

        .member-info .hero-subtitle {
            margin-bottom: 10px;
            color: var(--accent);
            font-weight: 500;
        }

        .member-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 15px;
            margin-bottom: 20px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: var(--foreground-secondary);
        }

        .meta-item ion-icon {
            color: var(--accent);
        }

        .member-bio {
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .contact-area {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .member-detail-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
        }

        @media (min-width: 992px) {
            .member-detail-content {
                grid-template-columns: 2fr 1fr;
            }
        }

        .content-main {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .content-aside {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .detail-card {
            padding: 30px;
        }

        .about-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
            margin-top: 20px;
        }

        .about-section h3 {
            color: var(--foreground-primary);
            margin-bottom: 15px;
            position: relative;
            padding-left: 20px;
        }

        .about-section h3::before {
            content: "";
            position: absolute;
            left: 0;
            top: 8px;
            width: 10px;
            height: 10px;
            background-color: var(--accent);
            border-radius: 50%;
        }

        .program-list,
        .achievement-list {
            list-style: disc;
            padding-left: 20px;
            margin-left: 10px;
            line-height: 1.8;
        }

        .vision-mission p {
            margin-bottom: 15px;
            line-height: 1.8;
        }

        .vision-mission ul {
            list-style: disc;
            padding-left: 20px;
            margin-left: 10px;
            line-height: 1.8;
        }

        .member-quote {
            padding: 20px;
            background-color: var(--bg-secondary);
            border-left: 5px solid var(--accent);
            font-style: italic;
            line-height: 1.8;
        }

        .related-posts {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        @media (min-width: 768px) {
            .related-posts {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .related-post-card {
            overflow: hidden;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .related-post-card .card-content {
            padding: 15px;
        }

        .department-members {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 20px;
        }

        .member-card {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .member-avatar {
            border-radius: 50%;
            overflow: hidden;
        }

        .member-name {
            color: var(--foreground-primary);
            transition: 0.25s ease;
        }

        .member-name:hover {
            color: var(--accent);
        }

        .member-position {
            color: var(--foreground-secondary);
            font-size: 0.9rem;
        }

        .cta-card {
            background-color: var(--bg-secondary);
            text-align: center;
            padding: 40px 20px;
        }

        .cta-card .card-title {
            margin-bottom: 20px;
        }

        .cta-card .card-text {
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .no-members-msg {
            color: var(--foreground-secondary);
            font-style: italic;
            text-align: center;
            padding: 20px 0;
        }

        .other-members {
            margin-top: 30px;
            position: relative;
        }

        .other-members .slider-card {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            background-color: var(--bg-primary);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: 0.3s ease;
        }

        .other-members .slider-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .other-members .slider-banner {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 15px;
            overflow: hidden;
            border: 3px solid var(--accent);
        }

        .other-members .slider-content {
            margin-top: 15px;
        }

        .other-members .slider-title {
            color: var(--foreground-primary);
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 5px;
            display: block;
        }

        .other-members .slider-subtitle {
            color: var(--foreground-secondary);
            font-size: 0.9rem;
        }

        .other-members .btn-group {
            position: absolute;
            top: -80px;
            right: 0;
            display: flex;
            gap: 10px;
        }

        @media (max-width: 768px) {
            .other-members .btn-group {
                top: -60px;
            }
        }
    </style>
    <section class="section member-detail" aria-labelledby="member-detail-title">
        <div class="container">
            <div class="member-header">
                <div class="member-banner">
                    <img src="<?php echo e(asset($member->image)); ?>" width="300" height="300" alt="<?php echo e($member->name); ?>"
                        class="profile-banner img-cover" />
                    <div class="member-social">
                        <?php if($member->instagram): ?>
                            <a href="https://instagram.com/<?php echo e($member->instagram); ?>" class="social-link" target="_blank">
                                <ion-icon name="logo-instagram" aria-hidden="true"></ion-icon>
                            </a>
                        <?php endif; ?>

                        <?php if($member->twitter): ?>
                            <a href="https://twitter.com/<?php echo e($member->twitter); ?>" class="social-link" target="_blank">
                                <ion-icon name="logo-twitter" aria-hidden="true"></ion-icon>
                            </a>
                        <?php endif; ?>

                        <?php if($member->linkedin): ?>
                            <a href="<?php echo e($member->linkedin); ?>" class="social-link" target="_blank">
                                <ion-icon name="logo-linkedin" aria-hidden="true"></ion-icon>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="member-info">
                    <p class="hero-subtitle"><?php echo e($member->position_name); ?></p>
                    <h1 class="headline headline-1 section-title" id="member-detail-title">
                        <?php echo e($member->name); ?>

                    </h1>
                    <div class="member-meta">
                        <div class="meta-item">
                            <ion-icon name="bookmark-outline" aria-hidden="true"></ion-icon>
                            <span>Bidang <?php echo e($member->position_name); ?></span>
                        </div>

                        <div class="meta-item">
                            <ion-icon name="school-outline" aria-hidden="true"></ion-icon>
                            <span><?php echo e($member->study_program ?? 'Program Studi FAI'); ?></span>
                        </div>

                        <div class="meta-item">
                            <ion-icon name="calendar-outline" aria-hidden="true"></ion-icon>
                            <span>Periode <?php echo e($member->period ?? '2023-2024'); ?></span>
                        </div>
                    </div>

                    <p class="member-bio">
                        <?php echo e($member->bio ?? 'Pengurus aktif PK IMM Al-Qossam yang berkomitmen menjalankan amanah dan kewajiban dakwah di kampus. Melalui peran strategis di organisasi, berupaya menjadi agen perubahan dan penggerak kebijakan yang bermanfaat untuk mahasiswa dan masyarakat luas.'); ?>

                    </p>

                    <div class="contact-area">
                        <?php if($member->email): ?>
                            <a href="mailto:<?php echo e($member->email); ?>" class="btn btn-primary">
                                <span class="span">Kirim Email</span>
                                <ion-icon name="mail-outline" aria-hidden="true"></ion-icon>
                            </a>
                        <?php endif; ?>

                        <?php if($member->phone): ?>
                            <a href="https://wa.me/<?php echo e(str_replace('+', '', $member->phone)); ?>" class="btn btn-secondary">
                                <span class="span">WhatsApp</span>
                                <ion-icon name="logo-whatsapp" aria-hidden="true"></ion-icon>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="member-detail-content">
                <div class="content-main">
                    <div class="card detail-card">
                        <h2 class="headline headline-2">Tentang <?php echo e($member->name); ?></h2>

                        <div class="about-content">
                            <div class="about-section">
                                <h3 class="headline headline-3">Program Kerja</h3>
                                <ul class="program-list">
                                    <?php if(isset($member->programs) && is_array($member->programs)): ?>
                                        <?php $__currentLoopData = $member->programs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $program): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($program); ?></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <li>Menyelenggarakan kajian rutin bidang <?php echo e($member->department); ?></li>
                                        <li>Membentuk tim untuk proyek <?php echo e($member->department); ?></li>
                                        <li>Pengembangan kapasitas anggota bidang</li>
                                        <li>Koordinasi dengan lembaga-lembaga terkait</li>
                                    <?php endif; ?>
                                </ul>
                            </div>

                            <div class="about-section">
                                <h3 class="headline headline-3">Prestasi & Kontribusi</h3>
                                <ul class="achievement-list">
                                    <?php if(isset($member->achievements) && is_array($member->achievements)): ?>
                                        <?php $__currentLoopData = $member->achievements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $achievement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($achievement); ?></li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <li>Aktif dalam kegiatan IMM Al-Qossam</li>
                                        <li>Berkontribusi dalam pengembangan organisasi</li>
                                        <li>Membangun jaringan dengan organisasi eksternal</li>
                                    <?php endif; ?>
                                </ul>
                            </div>

                            <div class="about-section">
                                <h3 class="headline headline-3">Visi & Misi</h3>
                                <div class="vision-mission">
                                    <p>
                                        <strong>Visi:</strong>
                                        <?php echo e($member->vision ?? 'Membangun bidang ' . $member->department . ' yang progresif, inovatif, dan responsif terhadap kebutuhan mahasiswa dan masyarakat.'); ?>

                                    </p>
                                    <p>
                                        <strong>Misi:</strong>
                                    </p>
                                    <ul>
                                        <?php if(isset($member->missions) && is_array($member->missions)): ?>
                                            <?php $__currentLoopData = $member->missions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li><?php echo e($mission); ?></li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php else: ?>
                                            <li>Mengoptimalkan fungsi bidang <?php echo e($member->department); ?> melalui
                                                program-program yang terukur</li>
                                            <li>Membangun kaderisasi yang berkelanjutan</li>
                                            <li>Memperluas jaringan kerjasama dengan berbagai pihak</li>
                                            <li>Meningkatkan kualitas sumber daya kader melalui pelatihan berkelanjutan</li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </div>

                            <div class="about-section">
                                <h3 class="headline headline-3">Motto</h3>
                                <blockquote class="member-quote">
                                    "<?php echo e($member->motto ?? 'Fastabiqul Khairat - Berlomba-lomba dalam kebaikan'); ?>"
                                </blockquote>
                            </div>
                        </div>
                    </div>

                    <?php if(count($relatedPosts) > 0): ?>
                        <div class="card detail-card">
                            <h2 class="headline headline-2">Tulisan Terkait</h2>

                            <div class="related-posts">
                                <?php $__currentLoopData = $relatedPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <article class="related-post-card">
                                        <figure class="card-banner img-holder" style="--width: 271; --height: 258">
                                            <img src="<?php echo e(asset($post->thumbnail)); ?>" width="271" height="258"
                                                loading="lazy" alt="<?php echo e($post->title); ?>" class="img-cover" />
                                        </figure>

                                        <div class="card-content">
                                            <a href="<?php echo e(route('posts.show', $post->slug)); ?>" class="card-btn"
                                                aria-label="<?php echo e($post->title); ?>">
                                                <ion-icon name="arrow-forward-outline" aria-hidden="true"></ion-icon>
                                            </a>

                                            <a href="<?php echo e(route('categories.show', $post->category->slug)); ?>"
                                                class="card-badge"><?php echo e($post->category->name); ?></a>

                                            <h3 class="headline headline-3">
                                                <a href="<?php echo e(route('posts.show', $post->slug)); ?>" class="card-title">
                                                    <?php echo e($post->title); ?>

                                                </a>
                                            </h3>

                                            <div class="card-meta">
                                                <div class="publish-date">
                                                    <ion-icon name="calendar-outline" aria-hidden="true"></ion-icon>
                                                    <time datetime="<?php echo e($post->created_at->format('Y-m-d')); ?>">
                                                        <?php echo e($post->created_at->format('d F Y')); ?>

                                                    </time>
                                                </div>
                                            </div>
                                        </div>
                                    </article>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="content-aside">
                    <div class="card aside-card">
                        <h3 class="headline headline-2 aside-title">
                            <span class="span">Anggota Bidang <?php echo e($member->position_name); ?></span>
                        </h3>

                        <div class="department-members">
                            <?php $__currentLoopData = $departmentMembers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $deptMember): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="member-card">
                                    <figure class="member-avatar img-holder" style="--width: 60; --height: 60">
                                        <img src="<?php echo e(asset($deptMember->image)); ?>" width="60" height="60"
                                            loading="lazy" alt="<?php echo e($deptMember->name); ?>"
                                            class="profile-banner img-cover" />
                                    </figure>

                                    <h4 class="headline headline-4">
                                        <a href="<?php echo e(route('organization.show', $deptMember->id)); ?>"
                                            class="member-name"><?php echo e($deptMember->name); ?></a>
                                    </h4>
                                    <p class="member-position"><?php echo e($deptMember->position_name); ?></p>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <?php if(count($departmentMembers) == 0): ?>
                                <p class="no-members-msg">Belum ada anggota lain di bidang ini.</p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="card aside-card cta-card">
                        <h3 class="headline headline-2 card-title">Bergabung dengan IMM Al-Qossam</h3>

                        <p class="card-text">
                            Jadilah bagian dari gerakan mahasiswa Islam yang memperjuangkan nilai-nilai kepemimpinan,
                            keilmuan, dan dakwah di kampus.
                        </p>

                        <a href="<?php echo e(route('login')); ?>" class="btn btn-primary">
                            <span class="span">Daftar Sekarang</span>
                            <ion-icon name="arrow-forward-outline" aria-hidden="true"></ion-icon>
                        </a>
                    </div>

                    <div class="card aside-card insta-card">
                        <a href="#" class="logo">
                            <?php if (isset($component)) { $__componentOriginalaa9b43b62d01e0a3e81a3c1ef58aaf32 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaa9b43b62d01e0a3e81a3c1ef58aaf32 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.logo.logo','data' => ['width' => '119','height' => '37']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('logo.logo'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['width' => '119','height' => '37']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaa9b43b62d01e0a3e81a3c1ef58aaf32)): ?>
<?php $attributes = $__attributesOriginalaa9b43b62d01e0a3e81a3c1ef58aaf32; ?>
<?php unset($__attributesOriginalaa9b43b62d01e0a3e81a3c1ef58aaf32); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaa9b43b62d01e0a3e81a3c1ef58aaf32)): ?>
<?php $component = $__componentOriginalaa9b43b62d01e0a3e81a3c1ef58aaf32; ?>
<?php unset($__componentOriginalaa9b43b62d01e0a3e81a3c1ef58aaf32); ?>
<?php endif; ?>
                        </a>

                        <p class="card-text">Ikuti Instagram Kami</p>

                        <?php
                            $instaItems = [
                                ['img' => 'home/assets/images/insta-post-1.png', 'alt' => 'insta post 1'],
                                ['img' => 'home/assets/images/insta-post-2.png', 'alt' => 'insta post 2'],
                                ['img' => 'home/assets/images/insta-post-3.png', 'alt' => 'insta post 3'],
                                ['img' => 'home/assets/images/insta-post-4.png', 'alt' => 'insta post 4'],
                                ['img' => 'home/assets/images/insta-post-5.png', 'alt' => 'insta post 5'],
                                ['img' => 'home/assets/images/insta-post-6.png', 'alt' => 'insta post 6'],
                            ];
                        ?>
                        <?php if (isset($component)) { $__componentOriginal610f20ea60880a5dde71303ec1187b33 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal610f20ea60880a5dde71303ec1187b33 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.insta.InstaList','data' => ['items' => $instaItems]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('insta.InstaList'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['items' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($instaItems)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal610f20ea60880a5dde71303ec1187b33)): ?>
<?php $attributes = $__attributesOriginal610f20ea60880a5dde71303ec1187b33; ?>
<?php unset($__attributesOriginal610f20ea60880a5dde71303ec1187b33); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal610f20ea60880a5dde71303ec1187b33)): ?>
<?php $component = $__componentOriginal610f20ea60880a5dde71303ec1187b33; ?>
<?php unset($__componentOriginal610f20ea60880a5dde71303ec1187b33); ?>
<?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="section org-structure" aria-labelledby="org-structure-label">
        <div class="container">
            <h2 class="headline headline-2 section-title" id="org-structure-label">
                <span class="span">Pengurus Lainnya</span>
            </h2>

            <p class="section-text">Kenali pengurus PK IMM Al-Qossam periode <?php echo e($member->period ?? '2023-2024'); ?></p>

            <div class="other-members slider" data-slider>
                <ul class="slider-list" data-slider-container>
                    <?php
                        $otherMembers = App\Models\OrganizationStructure::where('id', '!=', $member->id)
                            ->inRandomOrder()
                            ->take(6)
                            ->get();
                    ?>

                    <?php $__currentLoopData = $otherMembers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $otherMember): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="slider-item">
                            <a href="<?php echo e(route('organization.show', $otherMember->id)); ?>" class="slider-card">
                                <figure class="slider-banner img-holder" style="--width: 170; --height: 170">
                                    <img src="<?php echo e(asset($otherMember->image)); ?>" width="170" height="170"
                                        loading="lazy" alt="<?php echo e($otherMember->name); ?>"
                                        class="profile-banner img-cover" />
                                </figure>
                                <div class="slider-content">
                                    <span class="slider-title"><?php echo e($otherMember->name); ?></span>
                                    <p class="slider-subtitle"><?php echo e($otherMember->position_name); ?></p>
                                </div>
                            </a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>

                <div class="btn-group">
                    <button class="btn-icon" aria-label="previous" data-slider-prev>
                        <ion-icon name="arrow-back" aria-hidden="true"></ion-icon>
                    </button>

                    <button class="btn-icon" aria-label="next" data-slider-next>
                        <ion-icon name="arrow-forward" aria-hidden="true"></ion-icon>
                    </button>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.homeLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/structure/show.blade.php ENDPATH**/ ?>