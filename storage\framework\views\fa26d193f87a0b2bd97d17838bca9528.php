<?php $__env->startSection('title', $member->name . ' - Struktur Organisasi'); ?>

<?php $__env->startSection('content'); ?>
    <style>
        .member-detail {
            padding-top: 90px;
            margin-top: 50px
        }

        .member-header {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
            margin-bottom: 50px;
        }

        @media (min-width: 768px) {
            .member-header {
                grid-template-columns: 300px 1fr;
                align-items: start;
            }
        }

        .member-banner {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .member-social {
            position: absolute;
            bottom: 20px;
            left: 20px;
            display: flex;
            gap: 10px;
        }

        .social-link {
            width: 40px;
            height: 40px;
            background-color: var(--bg-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            color: var(--foreground-primary);
            font-size: 1.5rem;
            transition: 0.25s ease;
        }

        .social-link:hover {
            background-color: var(--accent);
            color: var(--white);
        }

        .member-info .hero-subtitle {
            margin-bottom: 10px;
            color: var(--accent);
            font-weight: 500;
        }

        .member-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 15px;
            margin-bottom: 20px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            color: var(--foreground-secondary);
        }

        .meta-item ion-icon {
            color: var(--accent);
        }

        .member-bio {
            line-height: 1.8;
            margin-bottom: 30px;
        }

        .contact-area {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .member-detail-content {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
        }

        @media (min-width: 992px) {
            .member-detail-content {
                grid-template-columns: 2fr 1fr;
            }
        }

        .content-main {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .content-aside {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .detail-card {
            padding: 30px;
        }

        .about-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
            margin-top: 20px;
        }

        .about-section h3 {
            color: var(--foreground-primary);
            margin-bottom: 15px;
            position: relative;
            padding-left: 20px;
        }

        .about-section h3::before {
            content: "";
            position: absolute;
            left: 0;
            top: 8px;
            width: 10px;
            height: 10px;
            background-color: var(--accent);
            border-radius: 50%;
        }

        .program-list,
        .achievement-list {
            list-style: disc;
            padding-left: 20px;
            margin-left: 10px;
            line-height: 1.8;
        }

        .vision-mission p {
            margin-bottom: 15px;
            line-height: 1.8;
        }

        .vision-mission ul {
            list-style: disc;
            padding-left: 20px;
            margin-left: 10px;
            line-height: 1.8;
        }

        .member-quote {
            padding: 20px;
            background-color: var(--bg-secondary);
            border-left: 5px solid var(--accent);
            font-style: italic;
            line-height: 1.8;
        }

        .related-posts {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        @media (min-width: 768px) {
            .related-posts {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .related-post-card {
            overflow: hidden;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .related-post-card .card-content {
            padding: 15px;
        }

        .department-members {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 20px;
        }

        .member-card {
            display: flex;
            gap: 15px;
            align-items: center;
            padding: 15px;
            border-radius: 10px;
            background-color: var(--bg-secondary);
            transition: 0.3s ease;
            margin-bottom: 10px;
        }

        .member-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .member-avatar {
            border-radius: 50%;
            overflow: hidden;
        }

        .member-name {
            color: var(--foreground-primary);
            transition: 0.25s ease;
        }

        .member-name:hover {
            color: var(--accent);
        }

        .member-position {
            color: var(--foreground-secondary);
            font-size: 0.9rem;
        }

        .cta-card {
            background-color: var(--bg-secondary);
            text-align: center;
            padding: 40px 20px;
        }

        .cta-card .card-title {
            margin-bottom: 20px;
        }

        .cta-card .card-text {
            margin-bottom: 30px;
            line-height: 1.8;
        }

        .no-members-msg {
            color: var(--foreground-secondary);
            font-style: italic;
            text-align: center;
            padding: 20px 0;
        }

        .other-members {
            margin-top: 30px;
            position: relative;
        }

        .other-members .slider-card {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            background-color: var(--bg-primary);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: 0.3s ease;
        }

        .other-members .slider-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .other-members .slider-banner {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 15px;
            overflow: hidden;
            border: 3px solid var(--accent);
        }

        .other-members .slider-content {
            margin-top: 15px;
        }

        .other-members .slider-title {
            color: var(--foreground-primary);
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 5px;
            display: block;
        }

        .other-members .slider-subtitle {
            color: var(--foreground-secondary);
            font-size: 0.9rem;
        }

        .other-members .btn-group {
            position: absolute;
            top: -80px;
            right: 0;
            display: flex;
            gap: 10px;
        }

        @media (max-width: 768px) {
            .other-members .btn-group {
                top: -60px;
            }
        }
    </style>
    <section class="section member-detail" aria-labelledby="member-detail-title">
        <div class="container">
            <div class="member-header">
                <div class="member-banner">
                    <?php if($member->image): ?>
                        <img src="<?php echo e(asset('storage/' . $member->image)); ?>" width="300" height="300"
                            alt="<?php echo e($member->name); ?>" class="profile-banner img-cover" />
                    <?php else: ?>
                        <div class="profile-banner img-cover"
                            style="width: 300px; height: 300px; background: linear-gradient(135deg, var(--accent), var(--accent-dark)); display: flex; align-items: center; justify-content: center; color: white; font-size: 4rem; font-weight: bold;">
                            <?php echo e(strtoupper(substr($member->name, 0, 1))); ?>

                        </div>
                    <?php endif; ?>
                </div>

                <div class="member-info">
                    <p class="hero-subtitle"><?php echo e($member->position_name); ?></p>
                    <h1 class="headline headline-1 section-title" id="member-detail-title">
                        <?php echo e($member->name); ?>

                    </h1>
                    <div class="member-meta">
                        <div class="meta-item">
                            <ion-icon name="bookmark-outline" aria-hidden="true"></ion-icon>
                            <span><?php echo e($member->position_name); ?></span>
                        </div>

                        <div class="meta-item">
                            <ion-icon name="people-outline" aria-hidden="true"></ion-icon>
                            <span>PK IMM Al-Qossam</span>
                        </div>


                        <?php if($member->order): ?>
                            <div class="meta-item">
                                <ion-icon name="trophy-outline" aria-hidden="true"></ion-icon>
                                <span>Urutan ke-<?php echo e($member->order); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="contact-area">
                        <a href="<?php echo e(route('organization.public.index')); ?>" class="btn btn-primary">
                            <span class="span">Lihat Struktur Lengkap</span>
                            <ion-icon name="people-outline" aria-hidden="true"></ion-icon>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="section org-structure" aria-labelledby="org-structure-label">
        <div class="container">
            <h2 class="headline headline-2 section-title" id="org-structure-label">
                <span class="span">Pengurus Lainnya</span>
            </h2>

            <p class="section-text">Kenali pengurus PK IMM Al-Qossam periode 2023-2024</p>

            <div class="other-members slider" data-slider>
                <ul class="slider-list" data-slider-container>
                    <?php
                        $otherMembers = App\Models\OrganizationStructure::where('id', '!=', $member->id)
                            ->where('is_active', true)
                            ->orderBy('order')
                            ->take(6)
                            ->get();
                    ?>

                    <?php $__currentLoopData = $otherMembers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $otherMember): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="slider-item">
                            <a href="<?php echo e(route('organization.show', $otherMember->id)); ?>" class="slider-card">
                                <figure class="slider-banner img-holder" style="--width: 170; --height: 170">
                                    <?php if($otherMember->image): ?>
                                        <img src="<?php echo e(asset('storage/' . $otherMember->image)); ?>" width="170"
                                            height="170" loading="lazy" alt="<?php echo e($otherMember->name); ?>"
                                            class="profile-banner img-cover" />
                                    <?php else: ?>
                                        <div class="profile-banner img-cover"
                                            style="width: 170px; height: 170px; background: linear-gradient(135deg, var(--accent), var(--accent-dark)); display: flex; align-items: center; justify-content: center; color: white; font-size: 3rem; font-weight: bold; border-radius: 50%;">
                                            <?php echo e(strtoupper(substr($otherMember->name, 0, 1))); ?>

                                        </div>
                                    <?php endif; ?>
                                </figure>
                                <div class="slider-content">
                                    <span class="slider-title"><?php echo e($otherMember->name); ?></span>
                                    <p class="slider-subtitle"><?php echo e($otherMember->position_name); ?></p>
                                </div>
                            </a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>

                <div class="btn-group">
                    <button class="btn-icon" aria-label="previous" data-slider-prev>
                        <ion-icon name="arrow-back" aria-hidden="true"></ion-icon>
                    </button>

                    <button class="btn-icon" aria-label="next" data-slider-next>
                        <ion-icon name="arrow-forward" aria-hidden="true"></ion-icon>
                    </button>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.homeLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/structure/show.blade.php ENDPATH**/ ?>