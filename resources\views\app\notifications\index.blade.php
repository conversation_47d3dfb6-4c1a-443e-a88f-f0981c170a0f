@extends('layouts.appLayout')

@section('title', 'Notifications')

@section('content')
    <div class="max-w-4xl mx-auto mt-10 p-6 bg-gray-800 rounded-xl shadow-2xl border border-gray-700">

        <x-notification.header title="Semua Notifikasi Anda" :showRefresh="true" :showTabs="true" :allCount="$notifications->total()"
            :unreadCount="count($unreadNotifications)" :readCount="count($readNotifications)" />

        <div id="all-notifications" class="space-y-4">
            @forelse ($notifications as $notification)
                <x-notification.item :notification="$notification" :showDebug="true" />
            @empty
                <x-notification.empty />
            @endforelse
        </div>

        <!-- Unread notifications -->
        <div id="unread-notifications" class="space-y-4 hidden">
            @if (count($unreadNotifications) > 0)
                @foreach ($unreadNotifications as $notification)
                    <x-notification.item :notification="$notification" :showDebug="true" />
                @endforeach
            @else
                <x-notification.empty icon="M5 13l4 4L19 7" message="Semua notifikasi sudah dibaca" />
            @endif
        </div>

        <!-- Read notifications -->
        <div id="read-notifications" class="space-y-4 hidden">
            @if (count($readNotifications) > 0)
                @foreach ($readNotifications as $notification)
                    <x-notification.item :notification="$notification" :showDebug="true" />
                @endforeach
            @else
                <x-notification.empty icon="M6 18L18 6M6 6l12 12" message="Belum ada notifikasi yang sudah dibaca" />
            @endif
        </div>

        <div class="mt-6" id="pagination-container">
            {{ $notifications->links() }}
        </div>
    </div>

    <x-notification.script />
@endsection
