@props([
    'name' => '',
    'id' => null,
    'label' => '',
    'required' => false,
    'disabled' => false,
    'error' => null,
    'helper' => null,
    'accept' => 'image/*',
    'currentFile' => null,
])

@php
    $inputId = $id ?? $name;
    $hasCurrentFile = !empty($currentFile);
@endphp

<div class="mb-4">
    @if ($label)
        <label for="{{ $inputId }}" class="block mb-2 text-sm font-medium text-indigo-200">
            {{ $label }}
            @if ($required)
                <span class="text-red-400">*</span>
            @endif
        </label>
    @endif

    <div class="flex flex-col items-center space-y-2">
        <!-- Upload Area (Hidden if has current file) -->
        <div id="{{ $inputId }}-upload-area"
            class="w-full flex items-center justify-center {{ $hasCurrentFile ? 'hidden' : '' }}">
            <label for="{{ $inputId }}"
                class="w-full flex flex-col items-center px-4 py-6 bg-gray-700/50 text-indigo-100 hover:bg-gray-700 rounded-lg border-2 border-dashed border-gray-500 hover:border-indigo-500 cursor-pointer transition-all duration-200">
                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                    <svg class="w-10 h-10 mb-3 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12">
                        </path>
                    </svg>
                    <p class="mb-2 text-sm text-center"><span class="font-semibold">Klik untuk upload</span> atau drag
                        and drop</p>
                    <p class="text-xs text-gray-400">{{ $helper ?? 'PNG, JPG atau GIF (Maks. 2MB)' }}</p>
                </div>

                <input type="file" name="{{ $name }}" id="{{ $inputId }}" accept="{{ $accept }}"
                    {{ $required && !$hasCurrentFile ? 'required' : '' }} {{ $disabled ? 'disabled' : '' }}
                    {{ $attributes->merge(['class' => 'hidden']) }} />
            </label>
        </div>

        <!-- Preview Container -->
        <div id="{{ $inputId }}-preview"
            class="preview-container {{ $hasCurrentFile ? '' : 'hidden' }} mt-2 w-full">
            <div class="relative p-2 rounded-lg border border-gray-600 bg-gray-800">
                @if ($hasCurrentFile)
                    <img src="{{ $currentFile }}" alt="Preview" class="h-40 mx-auto object-contain rounded"
                        id="{{ $inputId }}-preview-image">
                    <!-- Add hidden input to track that we have a current file -->
                    <input type="hidden" name="has_current_{{ $name }}" value="1">
                @else
                    <img src="#" alt="Preview" class="h-40 mx-auto object-contain rounded"
                        id="{{ $inputId }}-preview-image">
                @endif
                <button type="button" id="{{ $inputId }}-clear"
                    class="absolute top-2 right-2 p-1 bg-red-600 rounded-full text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
                <p class="text-xs text-center text-gray-400 mt-1.5 file-name">
                    @if ($hasCurrentFile)
                        {{ basename($currentFile) }}
                    @endif
                </p>
            </div>
        </div>
    </div>

    @if ($error)
        <p class="mt-1 text-sm text-red-500">{{ $error }}</p>
    @endif
</div>

@once
    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Setup file input previews
                document.querySelectorAll('input[type="file"]').forEach(inputElement => {
                    const fileId = inputElement.id;
                    const uploadArea = document.getElementById(`${fileId}-upload-area`);
                    const preview = document.getElementById(`${fileId}-preview`);
                    const previewImg = document.getElementById(`${fileId}-preview-image`);
                    const clearBtn = document.getElementById(`${fileId}-clear`);
                    const fileNameDisplay = preview.querySelector('.file-name');

                    if (preview && previewImg && clearBtn) {
                        inputElement.addEventListener('change', function(e) {
                            if (this.files && this.files[0]) {
                                const file = this.files[0];
                                const reader = new FileReader();

                                reader.onload = function(e) {
                                    previewImg.src = e.target.result;
                                    preview.classList.remove('hidden');
                                    uploadArea.classList.add('hidden');
                                    fileNameDisplay.textContent = file.name;
                                }

                                reader.readAsDataURL(file);
                            }
                        });

                        clearBtn.addEventListener('click', function() {
                            inputElement.value = '';
                            preview.classList.add('hidden');
                            uploadArea.classList.remove('hidden');
                            previewImg.src = '#';
                            fileNameDisplay.textContent = '';

                            // Clear the current file flag if it exists
                            const hiddenInput = document.querySelector(
                                `input[name="has_current_${inputElement.name}"]`);
                            if (hiddenInput) {
                                hiddenInput.value = '0';
                            }
                        });
                    }
                });
            });
        </script>
    @endpush
@endonce
