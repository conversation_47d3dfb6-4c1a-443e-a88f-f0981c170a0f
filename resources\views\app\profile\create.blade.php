@extends('layouts.appLayout')

@section('title', 'Complete Your Profile')

@section('content')
    <div class="max-w-2xl mx-auto mt-10 p-6 bg-gray-800 rounded-lg shadow-lg border border-gray-700">
        <x-auth.AuthTitle title="Lengkapi Profil Anda" desc="Isi informasi pribadi Anda untuk melanjutkan." />

        @if ($errors->any())
            <div class="p-4 mb-4 text-sm text-red-500 bg-red-100/10 rounded-lg">
                <ul class="list-disc pl-5">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form method="POST" action="{{ route('profile.store') }}" class="space-y-6" enctype="multipart/form-data">
            @csrf

            <x-form.file-upload name="avatar" label="Foto Profil" accept="image/*" required
                error="{{ $errors->first('avatar') }}" helper="Upload foto profil Anda (maks 2MB)" />

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <x-form.input name="address" label="Alamat" required placeholder="Masukkan Alamat"
                    value="{{ old('address') }}" error="{{ $errors->first('address') }}" />

                <x-form.select name="province" label="Provinsi" required placeholder="Pilih Provinsi"
                    error="{{ $errors->first('province') }}" />

                <x-form.select name="city" label="Kota/Kabupaten" required placeholder="Pilih Kota/Kabupaten"
                    error="{{ $errors->first('city') }}" />

                <x-form.input name="phone" label="Telepon" required placeholder="Masukkan Nomor Telepon"
                    value="{{ old('phone') }}" error="{{ $errors->first('phone') }}" />
            </div>

            <x-form.input name="hobbies" label="Hobi" required placeholder="Masukkan Hobi (pisahkan dengan koma)"
                value="{{ old('hobbies') }}" error="{{ $errors->first('hobbies') }}" />

            <x-form.textarea name="bio" label="Bio" rows="3" placeholder="Tulis bio singkat tentang diri Anda"
                value="{{ old('bio') }}" error="{{ $errors->first('bio') }}" />

            <div class="flex justify-end">
                <x-form.button type="submit" variant="primary">
                    Simpan Profil
                </x-form.button>
            </div>
        </form>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const provinceSelect = document.getElementById('province');
                const citySelect = document.getElementById('city');
                let provinces = [];

                // Pastikan elemen ada sebelum mencoba memanipulasinya
                if (!provinceSelect || !citySelect) return;

                // Disable city select sampai provinsi dipilih
                citySelect.disabled = true;

                // Tambahkan loading state
                provinceSelect.innerHTML = '<option value="">Loading provinsi...</option>';

                // Fungsi untuk menangani error
                function handleFetchError(error, element, message) {
                    console.error(error);
                    element.innerHTML = '<option value="">' + message + '</option>';
                }

                // Fetch provinsi menggunakan API wilayah Indonesia
                fetch('https://www.emsifa.com/api-wilayah-indonesia/api/provinces.json')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        provinces = data;
                        provinceSelect.innerHTML = '<option value="">-- Pilih Provinsi --</option>';

                        provinces.forEach(province => {
                            const option = document.createElement('option');
                            option.value = province.name;
                            option.textContent = province.name;
                            option.dataset.id = province.id;
                            provinceSelect.appendChild(option);
                        });

                        // Jika ada nilai old, pilih provinsi tersebut
                        const oldProvince = "{{ old('province') }}";
                        if (oldProvince) {
                            for (let i = 0; i < provinceSelect.options.length; i++) {
                                if (provinceSelect.options[i].value === oldProvince) {
                                    provinceSelect.selectedIndex = i;
                                    // Trigger change event untuk load kota
                                    const event = new Event('change');
                                    provinceSelect.dispatchEvent(event);
                                    break;
                                }
                            }
                        }
                    })
                    .catch(error => {
                        handleFetchError(error, provinceSelect, '-- Error loading provinsi --');
                    });

                // Event listener untuk provinsi
                provinceSelect.addEventListener('change', function() {
                    // Reset dan disable select kota
                    citySelect.innerHTML = '<option value="">Loading kota...</option>';
                    citySelect.disabled = true;

                    const selectedOption = this.options[this.selectedIndex];
                    if (!selectedOption || !selectedOption.dataset.id) {
                        citySelect.innerHTML = '<option value="">-- Pilih Provinsi Dulu --</option>';
                        return;
                    }

                    const provinceId = selectedOption.dataset.id;

                    // Fetch kota berdasarkan provinsi yang dipilih
                    fetch(`https://www.emsifa.com/api-wilayah-indonesia/api/regencies/${provinceId}.json`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Network response was not ok');
                            }
                            return response.json();
                        })
                        .then(cities => {
                            citySelect.innerHTML = '<option value="">-- Pilih Kota/Kabupaten --</option>';
                            citySelect.disabled = false;

                            cities.forEach(city => {
                                const option = document.createElement('option');
                                option.value = city.name;
                                option.textContent = city.name;
                                citySelect.appendChild(option);
                            });

                            // Jika ada nilai old, pilih kota tersebut
                            const oldCity = "{{ old('city') }}";
                            if (oldCity) {
                                for (let i = 0; i < citySelect.options.length; i++) {
                                    if (citySelect.options[i].value === oldCity) {
                                        citySelect.selectedIndex = i;
                                        break;
                                    }
                                }
                            }
                        })
                        .catch(error => {
                            handleFetchError(error, citySelect, '-- Error loading kota --');
                        });
                });

                // Validasi form sebelum submit
                const form = document.querySelector('form');
                form.addEventListener('submit', function(e) {
                    let isValid = true;

                    // Validasi provinsi dan kota
                    if (provinceSelect.value === '') {
                        isValid = false;
                        provinceSelect.classList.add('border-red-500');
                    } else {
                        provinceSelect.classList.remove('border-red-500');
                    }

                    if (citySelect.value === '') {
                        isValid = false;
                        citySelect.classList.add('border-red-500');
                    } else {
                        citySelect.classList.remove('border-red-500');
                    }

                    if (!isValid) {
                        e.preventDefault();
                        alert('Harap lengkapi semua field yang diperlukan');
                    }
                });
            });
        </script>
    @endpush
@endsection
