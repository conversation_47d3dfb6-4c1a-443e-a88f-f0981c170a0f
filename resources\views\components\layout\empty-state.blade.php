@props(['title', 'message', 'icon' => null, 'action' => null])

<div
    class="flex flex-col items-center justify-center py-12 bg-gray-700/50 rounded-xl border border-gray-600 text-center">
    @if ($icon)
        <div class="mb-4 text-indigo-400">
            {{ $icon }}
        </div>
    @else
        <svg class="h-16 w-16 text-indigo-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4">
            </path>
        </svg>
    @endif

    <h3 class="text-xl font-medium text-white mb-2">{{ $title }}</h3>
    <p class="text-gray-400 mb-6 max-w-md">{{ $message }}</p>

    @if ($action)
        <div>
            {{ $action }}
        </div>
    @endif
</div>
