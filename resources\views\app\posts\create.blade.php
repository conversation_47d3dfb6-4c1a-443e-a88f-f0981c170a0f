@extends('layouts.appLayout')

@section('title', 'Buat Postingan Baru')

@section('content')
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <x-layout.section title="Buat Postingan Baru">
            <div id="check-requirements">
                @php
                    $categoriesCount = \App\Models\Category::count();
                    $tagsCount = \App\Models\Tag::count();
                    $canCreatePost = $categoriesCount > 0 && $tagsCount > 0;
                @endphp

                @if (!$canCreatePost)
                    <div class="p-5 bg-gray-900/50 rounded-lg">
                        <h3 class="text-lg font-medium text-white mb-4">Persyaratan Postingan</h3>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div
                                    class="flex-shrink-0 h-6 w-6 {{ $categoriesCount > 0 ? 'text-green-500' : 'text-red-500' }}">
                                    @if ($categoriesCount > 0)
                                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 13l4 4L19 7" />
                                        </svg>
                                    @else
                                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    @endif
                                </div>
                                <div class="ml-3">
                                    <h3
                                        class="text-sm font-medium {{ $categoriesCount > 0 ? 'text-green-300' : 'text-red-300' }}">
                                        Kategori</h3>
                                    <div class="mt-1 text-sm text-gray-400">
                                        @if ($categoriesCount > 0)
                                            Tersedia {{ $categoriesCount }} kategori. <a
                                                href="{{ route('categories.index') }}"
                                                class="text-indigo-400 hover:text-indigo-300">Lihat kategori</a>
                                        @else
                                            Belum ada kategori. <a href="{{ route('categories.create') }}"
                                                class="text-indigo-400 hover:text-indigo-300">Buat kategori</a>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-6 w-6 {{ $tagsCount > 0 ? 'text-green-500' : 'text-red-500' }}">
                                    @if ($tagsCount > 0)
                                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 13l4 4L19 7" />
                                        </svg>
                                    @else
                                        <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    @endif
                                </div>
                                <div class="ml-3">
                                    <h3
                                        class="text-sm font-medium {{ $tagsCount > 0 ? 'text-green-300' : 'text-red-300' }}">
                                        Tag</h3>
                                    <div class="mt-1 text-sm text-gray-400">
                                        @if ($tagsCount > 0)
                                            Tersedia {{ $tagsCount }} tag. <a href="{{ route('tags.index') }}"
                                                class="text-indigo-400 hover:text-indigo-300">Lihat tag</a>
                                        @else
                                            Belum ada tag. <a href="{{ route('tags.create') }}"
                                                class="text-indigo-400 hover:text-indigo-300">Buat tag</a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6">
                            <p class="text-sm text-gray-300">Anda perlu membuat minimal satu kategori dan satu tag sebelum
                                dapat membuat postingan.</p>
                        </div>
                    </div>
                @else
                    <form action="{{ route('posts.store') }}" method="POST" enctype="multipart/form-data"
                        class="space-y-6">
                        @csrf

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="md:col-span-2">
                                <x-form.input name="title" label="Judul Postingan" placeholder="Masukkan judul..."
                                    required :error="$errors->first('title')" />
                            </div>

                            <div class="md:col-span-2">
                                <x-form.textarea name="description" label="Deskripsi Singkat"
                                    placeholder="Tuliskan deskripsi singkat postingan..." rows="3" required
                                    :error="$errors->first('description')" />
                            </div>

                            <div class="md:col-span-2">
                                <x-form.ckeditor-textarea name="content" label="Konten"
                                    placeholder="Tulis konten postingan anda disini..." rows="8" required
                                    :error="$errors->first('content')" :value="old('content')" />
                            </div>

                            <div class="md:col-span-2">
                                @php
                                    $categoryOptions = $categories
                                        ->map(function ($category) {
                                            return [
                                                'id' => $category->id,
                                                'name' => $category->title,
                                                'description' => $category->description,
                                                'image' => $category->banner,
                                            ];
                                        })
                                        ->toArray();
                                @endphp

                                <x-form.radio-group name="category_id" label="Pilih Kategori" :options="$categoryOptions"
                                    :selected="old('category_id')" />

                                @can('dashboard-admin')
                                    <div class="text-right mt-2">
                                        <a href="{{ route('categories.index') }}"
                                            class="text-xs text-indigo-400 hover:text-indigo-300">
                                            Kelola kategori
                                        </a>
                                    </div>
                                @endcan
                            </div>

                            <div class="md:col-span-2">
                                @php
                                    $tagOptions = $tags
                                        ->map(function ($tag) {
                                            return [
                                                'id' => $tag->id,
                                                'name' => $tag->title,
                                                'description' => $tag->description,
                                            ];
                                        })
                                        ->toArray();
                                @endphp

                                <x-form.checkbox-group name="tags" label="Pilih Tag (Maksimal 3)" :options="$tagOptions"
                                    :selected="$selectedTags" max="3" />

                                @can('dashboard-admin')
                                    <div class="text-right mt-2">
                                        <a href="{{ route('tags.index') }}"
                                            class="text-xs text-indigo-400 hover:text-indigo-300">
                                            Kelola tag
                                        </a>
                                    </div>
                                @endcan
                            </div>

                            <div class="md:col-span-2">
                                <x-form.file-upload name="image" label="Gambar Postingan"
                                    accept="image/jpeg,image/png,image/gif" :error="$errors->first('image')"
                                    helper="JPG, PNG, atau GIF. Maksimal 2MB." />
                            </div>
                        </div>

                        <div class="flex justify-end pt-6 border-t border-gray-700">
                            <x-form.button onclick="window.history.back()" type="button" variant="secondary"
                                class="mr-2">
                                Batal
                            </x-form.button>

                            <x-form.button type="submit" variant="primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7" />
                                </svg>
                                Simpan Postingan
                            </x-form.button>
                        </div>
                    </form>
                @endif
            </div>
        </x-layout.section>
    </div>
@endsection
