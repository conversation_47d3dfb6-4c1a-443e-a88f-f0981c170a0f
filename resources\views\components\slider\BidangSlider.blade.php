@props(['items'])

<ul class="slider-list" data-slider-container>
    @foreach ($items as $item)
        <li class="slider-item">
            <a href="{{ route('organization.show', $item['id']) }}" class="slider-card">
                <figure class="slider-banner img-holder" style="--width: 507; --height: 618">
                    <img src="{{ isset($item['img']) && strpos($item['img'], 'http') === 0 ? $item['img'] : asset($item['img']) }}"
                        width="507" height="618" loading="lazy"
                        alt="{{ $item['alt'] ?? $item['title'] }}" class="img-cover" />
                </figure>
                <div class="slider-content">
                    <span class="slider-title">{{ $item['title'] }}</span>
                    <p class="slider-subtitle">{{ $item['subtitle'] }}</p>
                </div>
            </a>
        </li>
    @endforeach
</ul>
