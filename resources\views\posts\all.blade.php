@extends('layouts.homeLayout')

@section('title', '<PERSON><PERSON><PERSON>')

@section('content')
    <style>
        /* Header Section Styles */
        .posts-header {
            padding-top: 160px;
            margin-bottom: 60px;
            position: relative;
            text-align: center;
        }

        .posts-header::after {
            content: "";
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--gradient-1);
            border-radius: var(--radius-pill);
        }

        .section-text-wrapper {
            position: relative;
            margin-bottom: 40px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .section-text {
            font-size: var(--fontSize-4);
            color: var(--text-wild-blue-yonder);
            line-height: 1.8;
            position: relative;
            z-index: 1;
            text-align: center;
        }

        .text-highlight {
            color: var(--text-carolina-blue);
            font-weight: var(--weight-bold);
        }

        .text-brand {
            color: var(--text-columbia-blue);
            font-style: italic;
        }

        /* Posts Grid Styles */
        .posts-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .grid-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
            gap: 30px;
        }

        @media (max-width: 768px) {
            .grid-list {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
        }

        /* Card Styles */
        .post-card {
            background: var(--bg-oxford-blue);
            border-radius: var(--radius-16);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid var(--border-prussian-blue);
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .post-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border-color: var(--bg-carolina-blue);
        }

        .card-banner {
            position: relative;
            overflow: hidden;
            height: 220px;
        }

        .card-banner::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, transparent 70%, rgba(0, 13, 26, 0.8));
            z-index: 1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .post-card:hover .card-banner::before {
            opacity: 1;
        }

        .img-cover {
            transition: transform 0.5s ease;
        }

        .post-card:hover .img-cover {
            transform: scale(1.05);
        }

        .card-content {
            padding: 25px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .card-badge {
            display: inline-block;
            background: var(--gradient-1);
            color: var(--text-white);
            border-radius: var(--radius-pill);
            padding: 6px 15px;
            font-size: var(--fontSize-7);
            font-weight: var(--weight-bold);
            margin-bottom: 15px;
            transition: transform 0.3s ease;
            box-shadow: 0 5px 10px rgba(14, 165, 233, 0.2);
        }

        .card-badge:hover {
            transform: translateY(-3px);
        }

        .card-title {
            margin: 0 0 15px 0;
            color: var(--text-columbia-blue);
            font-size: var(--fontSize-3);
            line-height: 1.4;
        }

        .card-title a {
            display: block;
            transition: color 0.3s ease;
        }

        .card-title a:hover {
            color: var(--text-carolina-blue);
        }

        .card-text {
            color: var(--text-wild-blue-yonder);
            font-size: var(--fontSize-6);
            line-height: 1.6;
            margin-bottom: 20px;
            flex-grow: 1;
        }

        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: auto;
            padding-top: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.05);
        }

        .card-tag {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .card-tag a {
            color: var(--text-carolina-blue);
            font-size: var(--fontSize-7);
            transition: all 0.3s ease;
            background-color: rgba(14, 165, 233, 0.1);
            padding: 4px 10px;
            border-radius: 4px;
        }

        .card-tag a:hover {
            background-color: var(--bg-carolina-blue);
            color: var(--text-white);
        }

        .interaction-buttons {
            display: flex;
            gap: 12px;
        }

        .btn-icon {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--bg-prussian-blue);
            border-radius: var(--radius-circle);
            color: var(--text-wild-blue-yonder);
            transition: all 0.3s ease;
            position: relative;
        }

        .btn-icon span {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--bg-carolina-blue);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-icon:hover {
            background: var(--gradient-1);
            color: var(--text-white);
            transform: translateY(-3px);
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: var(--bg-oxford-blue);
            border-radius: var(--radius-16);
            margin: 40px auto;
            max-width: 500px;
        }

        .empty-state ion-icon {
            font-size: 60px;
            color: var(--text-wild-blue-yonder);
            margin-bottom: 20px;
        }

        .empty-state p {
            font-size: var(--fontSize-4);
            color: var(--text-wild-blue-yonder);
        }

        /* Pagination Styles - Manual */
        .pagination {
            margin-top: 60px;
            display: flex;
            justify-content: center;
            padding-bottom: 60px;
        }

        .pagination-container {
            background: var(--bg-oxford-blue);
            padding: 12px 25px;
            border-radius: var(--radius-pill);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
            border: 1px solid var(--border-prussian-blue);
            position: relative;
            overflow: hidden;
        }

        .pagination-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--gradient-1);
        }

        .pagination-nav {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pagination-numbers {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .pagination-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 40px;
            height: 40px;
            padding: 0 12px;
            border-radius: var(--radius-circle);
            font-weight: var(--weight-semiBold);
            transition: all 0.3s ease;
            font-size: var(--fontSize-6);
            color: var(--text-wild-blue-yonder);
            cursor: pointer;
        }

        .pagination-btn:hover:not(.disabled):not(.active) {
            background: var(--bg-prussian-blue);
            color: var(--text-white);
            transform: translateY(-2px);
        }

        .pagination-btn.active {
            background: var(--gradient-1);
            color: var(--text-white);
            box-shadow: 0 5px 15px rgba(14, 165, 233, 0.3);
        }

        .pagination-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Pagination arrows styling */
        .pagination-btn ion-icon {
            width: 20px;
            height: 20px;
            stroke-width: 2;
        }

        /* Responsive adjustments */
        @media (max-width: 480px) {
            .pagination-container {
                padding: 10px 15px;
            }

            .pagination-btn {
                min-width: 36px;
                height: 36px;
                font-size: calc(var(--fontSize-6) - 1px);
            }

            .pagination-nav {
                gap: 5px;
            }

            .pagination-numbers {
                gap: 4px;
            }
        }
    </style>

    <section class="posts-header" aria-labelledby="all-posts-label">
        <div class="container">
            <h1 class="headline headline-1 section-title" id="all-posts-label">
                <span class="span">Semua Postingan</span>
            </h1>
            <div class="section-text-wrapper">
                <p class="section-text">
                    <span class="text-highlight">Jelajahi</span> seluruh postingan terbaru dari
                    <span class="text-brand">IMM Al-Qossam</span>
                </p>
            </div>
        </div>
    </section>

    <section class="section" id="all-posts">
        <div class="posts-container">
            <div class="grid-list">
                @forelse ($posts as $post)
                    <div class="post-card">
                        <figure class="card-banner">
                            <img src="{{ asset('storage/' . ($post->image ?? 'default.png')) }}" loading="lazy"
                                alt="{{ $post->title }}" class="img-cover" />
                        </figure>
                        <div class="card-content">
                            <a href="{{ route('categories.show', $post->category->slug ?? '-') }}" class="card-badge">
                                {{ $post->category->title ?? 'Tanpa Kategori' }}
                            </a>
                            <h3 class="card-title">
                                <a href="{{ route('posts.show', $post->slug) }}">{{ $post->title }}</a>
                            </h3>
                            <p class="card-text">{{ Str::limit(strip_tags($post->content), 120) }}</p>

                            <div class="card-footer">
                                <div class="card-tag">
                                    @foreach ($post->tags as $tag)
                                        <a href="{{ route('posts.by.tag', $tag->id) }}">#{{ $tag->title }}</a>
                                    @endforeach
                                </div>
                                <div class="interaction-buttons">
                                    <button class="btn-icon" onclick="handleLike({{ $post->id }})" title="Suka">
                                        <ion-icon name="thumbs-up-outline"></ion-icon>
                                        <span id="likeCount-{{ $post->id }}">{{ $post->likes_count ?? 0 }}</span>
                                    </button>
                                    <button class="btn-icon" onclick="handleDislike({{ $post->id }})"
                                        title="Tidak Suka">
                                        <ion-icon name="thumbs-down-outline"></ion-icon>
                                        <span
                                            id="dislikeCount-{{ $post->id }}">{{ $post->dislikes_count ?? 0 }}</span>
                                    </button>
                                    <a href="{{ route('posts.show', $post->slug) }}#comments" class="btn-icon"
                                        title="Komentar">
                                        <ion-icon name="chatbubble-outline"></ion-icon>
                                        <span id="commentCount-{{ $post->id }}">{{ $post->comments->count() }}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="empty-state">
                        <ion-icon name="document-text-outline"></ion-icon>
                        <p>Tidak ada postingan ditemukan.</p>
                    </div>
                @endforelse
            </div>
            <div class="pagination">
                <div class="pagination-container">
                    @if ($posts->hasPages())
                        <div class="pagination-nav">
                            {{-- Previous Page Link --}}
                            @if ($posts->onFirstPage())
                                <span class="pagination-btn disabled">
                                    <ion-icon name="chevron-back-outline"></ion-icon>
                                </span>
                            @else
                                <a href="{{ $posts->previousPageUrl() }}" class="pagination-btn">
                                    <ion-icon name="chevron-back-outline"></ion-icon>
                                </a>
                            @endif

                            {{-- Pagination Elements --}}
                            <div class="pagination-numbers">
                                @for ($i = 1; $i <= $posts->lastPage(); $i++)
                                    @if ($i == $posts->currentPage())
                                        <span class="pagination-btn active">{{ $i }}</span>
                                    @else
                                        <a href="{{ $posts->url($i) }}" class="pagination-btn">{{ $i }}</a>
                                    @endif
                                @endfor
                            </div>

                            {{-- Next Page Link --}}
                            @if ($posts->hasMorePages())
                                <a href="{{ $posts->nextPageUrl() }}" class="pagination-btn">
                                    <ion-icon name="chevron-forward-outline"></ion-icon>
                                </a>
                            @else
                                <span class="pagination-btn disabled">
                                    <ion-icon name="chevron-forward-outline"></ion-icon>
                                </span>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection
