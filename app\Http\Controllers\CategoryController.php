<?php

namespace App\Http\Controllers;

use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class CategoryController extends Controller
{
    public function index()
    {
        $categories = Category::withCount('posts')->paginate(10);
        return view('app.categories.index', compact('categories'));
    }

    public function create()
    {
        return view('app.categories.create');
    }

    public function store(Request $request)
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string',
                'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            Category::create([
                'title' => $request->title,
                'description' => $request->description,
                'banner' => $request->file('banner') ? $request->file('banner')->store('categories', 'public') : null,
                'slug' => Str::slug($request->title)
            ]);

            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Kategori berhasil dibuat!',
                    'redirect' => route('categories.index')
                ]);
            }

            return redirect()->route('categories.index')
                ->with('success', 'Kategori berhasil dibuat!');
        } catch (\Exception $e) {
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal membuat kategori. ' . $e->getMessage()
                ], 422);
            }

            return redirect()->back()
                ->withInput()
                ->with('error', 'Gagal membuat kategori: ' . $e->getMessage());
        }
    }

    public function edit(Category $category)
    {
        return view('app.categories.edit', compact('category'));
    }

    public function update(Request $request, Category $category)
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string',
                'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            $data = [
                'title' => $request->title,
                'description' => $request->description,
                'slug' => Str::slug($request->title)
            ];

            if ($request->hasFile('banner')) {
                if ($category->banner) {
                    Storage::disk('public')->delete($category->banner);
                }

                $data['banner'] = $request->file('banner')->store('categories', 'public');
            } elseif ($request->has('has_current_banner') && $request->has_current_banner == '0' && $category->banner) {
                Storage::disk('public')->delete($category->banner);
                $data['banner'] = null;
            }
            $category->update($data);

            return redirect()->route('categories.index')
                ->with('success', 'Kategori berhasil diperbarui!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Gagal memperbarui kategori: ' . $e->getMessage());
        }
    }

    public function destroy(Category $category)
    {
        try {
            if ($category->banner) {
                Storage::disk('public')->delete($category->banner);
            }

            $category->delete();

            return redirect()->route('categories.index')
                ->with('success', 'Kategori berhasil dihapus!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Gagal menghapus kategori: ' . $e->getMessage());
        }
    }

    public function show($slug)
    {
        $category = Category::where('slug', $slug)->firstOrFail();
        $posts = $category->posts()->with('tags')->latest()->paginate(10);
        return view('categories.show', compact('category', 'posts'));
    }
}
