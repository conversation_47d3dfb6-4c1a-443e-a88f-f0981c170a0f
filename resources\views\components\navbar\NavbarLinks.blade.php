@php
    $links = [
        ['href' => route('app.dashboard'), 'label' => 'Dashboard', 'can' => 'dashboard-user'],
        ['href' => route('posts.index'), 'label' => 'Postingan', 'can' => 'dashboard-user'],
        ['href' => route('organization.index'), 'label' => 'Struktural', 'can' => 'dashboard-admin'],
    ];
@endphp

<div class="flex flex-col space-y-1 sm:flex-row sm:space-x-4 sm:space-y-0">
    @foreach ($links as $link)
        @can($link['can'])
            <a href="{{ $link['href'] }}"
                class="rounded-md px-3 py-2 text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white
               {{ request()->url() == $link['href'] ? 'bg-gray-900 text-white' : '' }}"
                aria-current="{{ request()->url() == $link['href'] ? 'page' : false }}">
                {{ $link['label'] }}
            </a>
        @endcan
    @endforeach
</div>
