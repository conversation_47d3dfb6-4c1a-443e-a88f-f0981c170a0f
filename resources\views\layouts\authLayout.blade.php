<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'Auth') - {{ config('app.name') }}</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="{{ asset('js/sweet-alert.js') }}"></script>
    <link rel="shortcut icon" href="{{ asset('home/favicon_IMM_Al_Qossam.png') }}" type="image/x-icon">

</head>

<body class="bg-[hsla(222,44%,13%,1)]">
    <div class="min-h-screen flex items-center justify-center">
        <div class="absolute left-0 top-0 -z-10">
            <img src="{{ asset('home/assets/images/shadow-1.svg') }}" width="500" height="800" alt="Shadow 1"
                class="opacity-60 blur-sm" />
        </div>
        <div class="absolute right-0 bottom-0 -z-10">
            <img src="{{ asset('home/assets/images/shadow-2.svg') }}" width="500" height="500" alt="Shadow 2"
                class="opacity-60 blur-sm" />
        </div>
        <div class="relative w-full md:w-1/2">
            {{-- Pattern SVG as background --}}
            <img src="{{ asset('home/assets/images/pattern-2.svg') }}" width="27" height="26" alt="shape"
                class="absolute left-5 -top-8 w-10 h-10 animate-bounce pointer-events-none select-none"
                style="z-index:1;" />
            <img src="{{ asset('home/assets/images/pattern-3.svg') }}" width="27" height="26" alt="shape"
                class="absolute right-5 -bottom-5 w-10 h-10 animate-bounce pointer-events-none select-none"
                style="z-index:1;" />
            {{-- Form/content on top --}}
            <div
                class="relative z-10 flex flex-col items-center mb-8 backdrop-blur-lg rounded-2xl shadow-2xl px-8 py-10 md:max-w-md mx-auto ">
                @yield('content')
            </div>
        </div>
    </div>
    <!-- Flash Messages -->
    <script>
        @if (session('success'))
            showSuccessAlert(
                "{{ session('success') }}",
                "{{ session('redirect') ?? '' }}"
            );
        @endif

        @if (session('error'))
            showErrorAlert("{{ session('error') }}");
        @endif

        @if ($errors->any())
            showErrorAlert("{{ $errors->first() }}");
        @endif
    </script>
</body>

</html>
