<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Notifications\LoginSuccessNotification;
use App\Notifications\ProfileCompletionNotification;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    public function showLogin()
    {
        return view('auth.login');
    }

    public function showRegister()
    {
        return view('auth.register');
    }

    public function login(Request $request)
    {
        $messages = [
            'email.required' => 'Email wajib diisi',
            'email.email' => 'Format email tidak valid',
            'password.required' => 'Password wajib diisi',
        ];

        try {
            $credentials = $request->validate([
                'email' => ['required', 'email'],
                'password' => ['required'],
            ], $messages);

            if (Auth::attempt($credentials)) {
                $request->session()->regenerate();

                if ($request->ajax()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Login berhasil! Selamat datang kembali.',
                        'redirect' => route('app.dashboard')
                    ]);
                }

                return redirect()
                    ->intended(route('app.dashboard'))
                    ->with('success', 'Login berhasil! Selamat datang kembali.');
            }

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email atau password salah!'
                ], 422);
            }

            return back()
                ->withInput($request->only('email'))
                ->withErrors(['email' => 'Email atau password salah!']);
        } catch (ValidationException $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi gagal',
                    'errors' => $e->errors()
                ], 422);
            }

            throw $e;
        }
    }

    public function register(Request $request)
    {
        $messages = [
            'name.required' => 'Nama wajib diisi',
            'name.string' => 'Nama harus berupa teks',
            'name.max' => 'Nama tidak boleh lebih dari :max karakter',
            'email.required' => 'Email wajib diisi',
            'email.email' => 'Format email tidak valid',
            'email.max' => 'Email tidak boleh lebih dari :max karakter',
            'email.unique' => 'Email sudah terdaftar',
            'password.required' => 'Password wajib diisi',
            'password.string' => 'Password harus berupa teks',
            'password.min' => 'Password minimal :min karakter',
            'password.confirmed' => 'Konfirmasi password tidak cocok',
        ];

        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users',
                'password' => 'required|string|min:8|confirmed',
            ], $messages);

            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => Hash::make($validated['password']),
            ]);

            $user->notify(new LoginSuccessNotification($user));
            $user->notify(new ProfileCompletionNotification($user));

            Auth::login($user);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Registrasi berhasil! Silakan lengkapi profil Anda.',
                    'redirect' => route('profile.create')
                ]);
            }

            return redirect()
                ->route('profile.create')
                ->with('success', 'Registrasi berhasil! Silakan lengkapi profil Anda.');

        } catch (ValidationException $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validasi gagal',
                    'errors' => $e->errors()
                ], 422);
            }

            throw $e;
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat registrasi.'
                ], 500);
            }

            return back()
                ->withInput($request->only('name', 'email'))
                ->withErrors(['error' => 'Terjadi kesalahan saat registrasi.']);
        }
    }

    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()
            ->route('login')
            ->with('success', 'Anda berhasil keluar dari sistem!');
    }
}


