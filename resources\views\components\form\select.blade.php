@props([
    'name' => '',
    'id' => null,
    'label' => '',
    'placeholder' => 'Pilih...',
    'required' => false,
    'disabled' => false,
    'error' => null,
    'helper' => null,
    'options' => [],
])

@php
    $inputId = $id ?? $name;
@endphp

<div class="mb-4">
    @if ($label)
        <label for="{{ $inputId }}" class="block mb-2 text-sm font-medium text-indigo-200">
            {{ $label }}
            @if ($required)
                <span class="text-red-400">*</span>
            @endif
        </label>
    @endif

    <div class="relative">
        <select name="{{ $name }}" id="{{ $inputId }}" {{ $required ? 'required' : '' }}
            {{ $disabled ? 'disabled' : '' }}
            {{ $attributes->merge([
                'class' =>
                    'block w-full px-4 py-2.5 bg-gray-700 border appearance-none ' .
                    ($error
                        ? 'border-red-500 focus:ring-red-500'
                        : 'border-gray-600 focus:border-indigo-500 focus:ring-indigo-500') .
                    ' rounded-lg text-sm text-indigo-100 focus:outline-none focus:ring-1 shadow-sm',
            ]) }}>
            @if ($placeholder)
                <option value="" disabled {{ !isset($attributes['value']) ? 'selected' : '' }}>{{ $placeholder }}
                </option>
            @endif

            @if (is_array($options))
                @foreach ($options as $value => $label)
                    <option value="{{ $value }}"
                        {{ (string) $value === (string) ($attributes['value'] ?? '') ? 'selected' : '' }}>
                        {{ $label }}
                    </option>
                @endforeach
            @else
                {{ $slot }}
            @endif
        </select>

        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
            </svg>
        </div>
    </div>

    @if ($error)
        <p class="mt-1 text-sm text-red-500">{{ $error }}</p>
    @elseif ($helper)
        <p class="mt-1 text-sm text-gray-400">{{ $helper }}</p>
    @endif
</div>
