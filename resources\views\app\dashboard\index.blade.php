@extends('layouts.appLayout')

@section('title', 'Dashboard')

@push('styles')
    <style>
        .chartjs-tooltip {
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border-radius: 3px;
            padding: 5px 10px;
            font-size: 12px;
        }

        .dashboard-container {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stat-card {
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
        }

        /* Perbaikan untuk tombol filter pada mobile */
        .chart-filter {
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
        }

        @media (max-width: 640px) {
            .chart-container {
                height: 250px !important;
            }

            .chart-filter {
                font-size: 0.7rem;
                padding: 0.35rem 0.75rem;
                margin: 0 0.25rem;
            }

            .top-posts-info {
                font-size: 0.75rem;
            }
        }
    </style>
@endpush

@section('content')
    <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8 space-y-8 dashboard-container">

        <x-dashboard.welcome-card :user="$user" />

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <x-dashboard.stat-card title="Total Postingan" :value="$postStats['total_posts']" :percentage="$postStats['posts_percentage']" color="indigo"
                class="stat-card" />
            <x-dashboard.stat-card title="Total Tontonan" :value="number_format($postStats['total_views'])" :percentage="$postStats['views_percentage']" color="purple"
                class="stat-card" />
            <x-dashboard.stat-card title="Total Komentar" :value="number_format($postStats['total_comments'])" :percentage="$postStats['comments_percentage']" color="pink"
                class="stat-card" />
        </div>

        <div class="bg-gray-800/80 rounded-xl p-1">
            <div class="p-5">
                <div class="p-6 bg-gray-800 rounded-xl shadow-lg border border-gray-700">
                    <div class="flex items-center justify-between mb-6 flex-wrap">
                        <div>
                            <h3 class="text-lg font-semibold text-indigo-300">Grafik Performa Postingan</h3>
                            <p class="text-gray-400 text-sm mt-1">Data performa postingan berdasarkan pengunjung, komentar,
                                dan like terbanyak</p>
                        </div>
                        <div class="flex space-x-2 mt-4 sm:mt-2 w-full sm:w-auto justify-center">
                            <button id="monthlyFilter"
                                class="chart-filter active px-3 py-2 text-xs rounded-full bg-indigo-700 text-indigo-100 hover:bg-indigo-600 transition min-w-[80px] touch-manipulation">
                                Bulan Ini
                            </button>
                            <button id="quarterlyFilter"
                                class="chart-filter px-3 py-2 text-xs rounded-full bg-gray-700 text-gray-300 hover:bg-indigo-700 hover:text-indigo-100 transition min-w-[80px] touch-manipulation">
                                Triwulan
                            </button>
                            <button id="yearlyFilter"
                                class="chart-filter px-3 py-2 text-xs rounded-full bg-gray-700 text-gray-300 hover:bg-indigo-700 hover:text-indigo-100 transition min-w-[80px] touch-manipulation">
                                Tahunan
                            </button>
                        </div>
                    </div>
                    <div style="height: 300px;" class="chart-container relative">
                        <div
                            class="absolute inset-0 flex items-center justify-center chart-loading opacity-0 pointer-events-none transition-opacity">
                            <div
                                class="w-10 h-10 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin">
                            </div>
                        </div>
                        <canvas id="postChart" class="transition-opacity duration-500"></canvas>
                    </div>
                    <div class="mt-4 text-sm text-gray-300 bg-gray-700/50 p-3 rounded-lg top-posts-info">
                        <h4 class="font-medium text-indigo-300 mb-2">Postingan Terbaik:</h4>
                        <div class="mb-1">
                            <span class="inline-block w-3 h-3 rounded-full bg-indigo-500 mr-2"></span>
                            <span class="font-medium">Pengunjung Terbanyak:</span>
                            <span class="text-gray-400">{{ $userPanelData['top_posts']['top_views']['title'] }}
                                ({{ $userPanelData['top_posts']['top_views']['count'] }} views)</span>
                        </div>
                        <div  class="mb-1">
                            <span class="inline-block w-3 h-3 rounded-full bg-pink-500 mr-2"></span>
                            <span class="font-medium">Komentar Terbanyak:</span>
                            @if (isset($userPanelData['top_posts']['top_comments']) && !empty($userPanelData['top_posts']['top_comments']['title']))
                                <span class="text-gray-400">{{ $userPanelData['top_posts']['top_comments']['title'] }}
                                    ({{ $userPanelData['top_posts']['top_comments']['count'] }} komentar)</span>
                            @else
                                <span class="text-gray-400">Belum ada postingan</span>
                            @endif
                        </div>
                        <div  class="mb-1">
                            <span class="inline-block w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            <span class="font-medium">Like Terbanyak:</span>
                            @if (isset($userPanelData['top_posts']['top_likes']) && !empty($userPanelData['top_posts']['top_likes']['title']))
                                <span class="text-gray-400">{{ $userPanelData['top_posts']['top_likes']['title'] }}
                                    ({{ $userPanelData['top_posts']['top_likes']['count'] }} likes)</span>
                            @else
                                <span class="text-gray-400">Belum ada postingan</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @can('dashboard-admin')
            <div class="bg-gradient-to-r from-indigo-900/20 to-purple-900/20 rounded-xl p-1.5 shadow-lg">
                <x-dashboard.admin.panel :stats="$adminStats" />
            </div>
        @endcan

        @if (auth()->user()->role === 'user')
            <div class="bg-gradient-to-r from-indigo-900/20 to-purple-900/20 rounded-xl p-1.5 shadow-lg">
                <x-dashboard.user.panel :userPanelData="$userPanelData" />
            </div>
        @endif
    </div>
@endsection

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        const postChartData = {
            labels: @json($chartData['post_chart']['labels']),
            data: @json($chartData['post_chart']['data'])
        };

        const weeklyData = {
            labels: @json($chartData['weekly_data']['labels']),
            views: @json($chartData['weekly_data']['views']),
            comments: @json($chartData['weekly_data']['comments']),
            likes: @json($chartData['weekly_data']['likes']),
            topPostsTitles: @json($chartData['weekly_data']['top_posts_titles']),
            topCommentedPostsTitles: @json($chartData['weekly_data']['top_commented_posts_titles']),
            topLikedPostsTitles: @json($chartData['weekly_data']['top_liked_posts_titles'])
        };

        const quarterlyData = {
            labels: @json($chartData['quarterly_data']['labels']),
            views: @json($chartData['quarterly_data']['views']),
            comments: @json($chartData['quarterly_data']['comments']),
            likes: @json($chartData['quarterly_data']['likes']),
            topPostsTitles: @json($chartData['quarterly_data']['top_posts_titles']),
            topCommentedPostsTitles: @json($chartData['quarterly_data']['top_commented_posts_titles']),
            topLikedPostsTitles: @json($chartData['quarterly_data']['top_liked_posts_titles'])
        };

        const yearlyData = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'],
            views: @json($chartData['monthly_views_chart']['data']),
            comments: @json($chartData['monthly_comments_chart']['data']),
            likes: @json($chartData['monthly_likes_chart']['data']),
            topPostsTitles: @json($chartData['top_posts_titles']),
            topCommentedPostsTitles: @json($chartData['top_commented_posts_titles']),
            topLikedPostsTitles: @json($chartData['top_liked_posts_titles'])
        };

        const createMultiLineChartConfig = (labels, datasets) => ({
            type: 'line',
            data: {
                labels: labels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 1500,
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(107, 114, 128, 0.3)',
                            borderColor: 'rgba(107, 114, 128, 0.3)'
                        },
                        ticks: {
                            color: '#d1d5db',
                            precision: 0
                        }
                    },
                    x: {
                        grid: {
                            display: false,
                            borderColor: 'rgba(107, 114, 128, 0.3)'
                        },
                        ticks: {
                            color: '#d1d5db',
                            callback: function(value, index, values) {
                                if (window.innerWidth < 640) {
                                    const label = this.getLabelForValue(value);
                                    return label.length > 3 ? label.substring(0, 3) : label;
                                }
                                return this.getLabelForValue(value);
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            color: '#d1d5db',
                            boxWidth: 12,
                            padding: 20,
                            font: {
                                size: window.innerWidth < 640 ? 10 : 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(15, 23, 42, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        padding: 12,
                        cornerRadius: 8,
                        displayColors: true,
                        boxPadding: 4,
                        bodyFont: {
                            family: 'Poppins, system-ui, sans-serif'
                        },
                        titleFont: {
                            family: 'Poppins, system-ui, sans-serif',
                            weight: 'bold'
                        }
                    }
                },
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
            }
        });

        let postChart;

        document.addEventListener('DOMContentLoaded', function() {
            const postCtx = document.getElementById('postChart')?.getContext('2d');
            if (postCtx) {
                initializeMonthlyView(postCtx);
                updateTopPostsInfo('monthly');

                document.getElementById('monthlyFilter').addEventListener('click', function() {
                    updateChartView('monthly', postCtx);
                });

                document.getElementById('quarterlyFilter').addEventListener('click', function() {
                    updateChartView('quarterly', postCtx);
                });

                document.getElementById('yearlyFilter').addEventListener('click', function() {
                    updateChartView('yearly', postCtx);
                });
            }

            window.addEventListener('resize', function() {
                if (postChart) {
                    postChart.resize();
                }
            });
        });

        function initializeMonthlyView(ctx) {
            const datasets = [{
                    label: 'Pengunjung',
                    data: weeklyData.views,
                    borderColor: '#818cf8', // indigo
                    backgroundColor: 'rgba(129, 140, 248, 0.2)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                },
                {
                    label: 'Komentar',
                    data: weeklyData.comments,
                    borderColor: '#f472b6', // pink
                    backgroundColor: 'rgba(244, 114, 182, 0.2)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                },
                {
                    label: 'Like',
                    data: weeklyData.likes,
                    borderColor: '#34d399', // green
                    backgroundColor: 'rgba(52, 211, 153, 0.2)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                }
            ];

            postChart = new Chart(ctx, createMultiLineChartConfig(weeklyData.labels, datasets));
        }

        function updateChartView(period, ctx) {
            const chartLoading = document.querySelector('.chart-loading');
            chartLoading.classList.remove('opacity-0');
            chartLoading.classList.add('opacity-100');

            const filters = document.querySelectorAll('.chart-filter');
            filters.forEach(f => {
                f.classList.remove('active', 'bg-indigo-700', 'text-indigo-100');
                f.classList.add('bg-gray-700', 'text-gray-300');
            });
            document.getElementById(`${period}Filter`).classList.add('active', 'bg-indigo-700', 'text-indigo-100');
            document.getElementById(`${period}Filter`).classList.remove('bg-gray-700', 'text-gray-300');

            let labels, viewsData, commentsData, likesData;

            if (period === 'monthly') {
                labels = weeklyData.labels;
                viewsData = weeklyData.views;
                commentsData = weeklyData.comments;
                likesData = weeklyData.likes;
            } else if (period === 'quarterly') {
                labels = quarterlyData.labels;
                viewsData = quarterlyData.views;
                commentsData = quarterlyData.comments;
                likesData = quarterlyData.likes;
            } else { // yearly
                labels = yearlyData.labels;
                viewsData = yearlyData.views;
                commentsData = yearlyData.comments;
                likesData = yearlyData.likes;
            }

            const datasets = [{
                    label: 'Pengunjung',
                    data: viewsData,
                    borderColor: '#818cf8', // indigo
                    backgroundColor: 'rgba(129, 140, 248, 0.2)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                },
                {
                    label: 'Komentar',
                    data: commentsData,
                    borderColor: '#f472b6', // pink
                    backgroundColor: 'rgba(244, 114, 182, 0.2)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                },
                {
                    label: 'Like',
                    data: likesData,
                    borderColor: '#34d399', // green
                    backgroundColor: 'rgba(52, 211, 153, 0.2)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                }
            ];

            postChart.destroy();
            postChart = new Chart(ctx, createMultiLineChartConfig(labels, datasets));

            setTimeout(() => {
                chartLoading.classList.remove('opacity-100');
                chartLoading.classList.add('opacity-0');
            }, 800);
        }

        function updateTopPostsInfo(period) {
            let topViewsTitle, topViewsCount;
            let topCommentsTitle, topCommentsCount;
            let topLikesTitle, topLikesCount;

            if (period === 'monthly') {
                topViewsTitle = weeklyData.topPostsTitles[0] || 'Belum ada postingan';
                topCommentsTitle = weeklyData.topCommentedPostsTitles[0] || 'Belum ada postingan';
                topLikesTitle = weeklyData.topLikedPostsTitles[0] || 'Belum ada postingan';

                topViewsCount = Math.max(...weeklyData.views) || 0;
                topCommentsCount = Math.max(...weeklyData.comments) || 0;
                topLikesCount = Math.max(...weeklyData.likes) || 0;
            } else if (period === 'quarterly') {
                topViewsTitle = quarterlyData.topPostsTitles[0] || 'Belum ada postingan';
                topCommentsTitle = quarterlyData.topCommentedPostsTitles[0] || 'Belum ada postingan';
                topLikesTitle = quarterlyData.topLikedPostsTitles[0] || 'Belum ada postingan';

                topViewsCount = Math.max(...quarterlyData.views) || 0;
                topCommentsCount = Math.max(...quarterlyData.comments) || 0;
                topLikesCount = Math.max(...quarterlyData.likes) || 0;
            } else { // yearly
                topViewsTitle = yearlyData.topPostsTitles[0] || 'Belum ada postingan';
                topCommentsTitle = yearlyData.topCommentedPostsTitles[0] || 'Belum ada postingan';
                topLikesTitle = yearlyData.topLikedPostsTitles[0] || 'Belum ada postingan';

                topViewsCount = Math.max(...yearlyData.views) || 0;
                topCommentsCount = Math.max(...yearlyData.comments) || 0;
                topLikesCount = Math.max(...yearlyData.likes) || 0;
            }

            const topViewsPostEl = document.getElementById('topViewsPost');
            const topCommentsPostEl = document.getElementById('topCommentsPost');
            const topLikesPostEl = document.getElementById('topLikesPost');

            if (topViewsPostEl) {
                topViewsPostEl.querySelector('.text-gray-400').textContent =
                    `${topViewsTitle} (${topViewsCount} views)`;
            }

            if (topCommentsPostEl) {
                topCommentsPostEl.querySelector('.text-gray-400').textContent =
                    `${topCommentsTitle} (${topCommentsCount} komentar)`;
            }

            if (topLikesPostEl) {
                topLikesPostEl.querySelector('.text-gray-400').textContent =
                    `${topLikesTitle} (${topLikesCount} likes)`;
            }
        }
    </script>
@endpush
