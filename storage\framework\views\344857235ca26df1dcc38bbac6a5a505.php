<?php $__env->startSection('title', 'Semu<PERSON> Postingan'); ?>

<?php $__env->startSection('content'); ?>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('dashboard-admin')): ?>
            <div class="container mx-auto py-6 my-20">
                <h1 class="text-2xl font-bold mb-6 text-indigo-700">Postingan yang Menunggu Persetujuan</h1>
                <?php if($pendingPosts->isEmpty()): ?>
                    <div class="bg-yellow-100 text-yellow-800 p-4 rounded">
                        Tidak ada postingan menunggu persetujuan
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-indigo-400">
                            <thead class="bg-indigo-600 text-white">
                                <tr>
                                    <th class="px-6 py-3 text-left text-white">User</th>
                                    <th class="px-6 py-3 text-left text-white">Judul</th>
                                    <th class="px-6 py-3 text-left text-white">Tanggal</th>
                                    <th class="px-6 py-3 text-left text-white">Aksi</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-indigo-400">
                                <?php $__currentLoopData = $pendingPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="px-6 py-4 text-sm text-white"><?php echo e($post->user->name); ?></td>
                                        <td class="px-6 py-4 text-sm text-white">
                                            <a href="<?php echo e(route('posts.show', $post->slug)); ?>"
                                                class="hover:text-blue-600"><?php echo e($post->title); ?></a>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-white"><?php echo e($post->created_at->format('d M Y H:i')); ?>

                                        </td>
                                        <td class="px-6 py-4 text-sm text-white">
                                            <form id="approve-form-<?php echo e($post->id); ?>"
                                                action="<?php echo e(route('posts.approve', $post->id)); ?>" method="POST" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('PATCH'); ?>
                                                <button type="button"
                                                    onclick="confirmApprove(<?php echo e($post->id); ?>, '<?php echo e($post->title); ?>')"
                                                    class="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                                                    Setujui
                                                </button>
                                            </form>
                                            <form id="reject-form-<?php echo e($post->id); ?>"
                                                action="<?php echo e(route('posts.reject', $post->id)); ?>" method="POST" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <button type="button"
                                                    onclick="confirmReject(<?php echo e($post->id); ?>, '<?php echo e($post->title); ?>')"
                                                    class="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                                                    Tolak
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
            <h1 class="text-3xl font-bold text-white mb-4 md:mb-0">
                <span class="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('dashboard-admin')): ?>
                        Semua Postingan
                    <?php else: ?>
                        Postingan Saya
                    <?php endif; ?>
                </span>
            </h1>

            <div class="flex space-x-2">
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('dashboard-admin')): ?>
                    <a href="<?php echo e(route('categories.index')); ?>"
                        class="inline-flex items-center px-3 py-2 border border-indigo-500 rounded-md text-sm font-medium text-indigo-500 bg-transparent hover:bg-indigo-500 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 focus:ring-offset-gray-800 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                        Kategori
                    </a>

                    <a href="<?php echo e(route('tags.index')); ?>"
                        class="inline-flex items-center px-3 py-2 border border-indigo-500 rounded-md text-sm font-medium text-indigo-500 bg-transparent hover:bg-indigo-500 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 focus:ring-offset-gray-800 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                        Tag
                    </a>
                <?php endif; ?>

                <a href="<?php echo e(route('posts.create')); ?>"
                    class="inline-flex items-center px-3 py-2 bg-indigo-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 focus:ring-offset-gray-800 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Postingan Baru
                </a>
            </div>
        </div>

        <div class="mb-6">
            <form action="<?php echo e(route('posts.index')); ?>" method="GET" class="flex">
                <div class="relative flex-grow">
                    <input type="text" name="search" value="<?php echo e($search ?? ''); ?>"
                        class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-l-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Cari postingan...">
                </div>
                <button type="submit"
                    class="px-4 py-2 bg-indigo-600 border border-transparent rounded-r-md text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </button>
            </form>
        </div>

        <?php if(count($posts) > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if (isset($component)) { $__componentOriginal352a17f6379e55178f396c3d115b7a53 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal352a17f6379e55178f396c3d115b7a53 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.post.card','data' => ['post' => $post]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('post.card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($post)]); ?>
                        <div class="flex space-x-2">
                            <a href="<?php echo e(route('posts.edit', $post->id)); ?>" class="text-indigo-400 hover:text-indigo-300"
                                title="Edit">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                                    stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                            </a>

                            <?php if($post->is_published || Auth::user()->role === 'admin'): ?>
                                <a href="<?php echo e(route('posts.show', $post->slug)); ?>" target="_blank"
                                    class="text-green-400 hover:text-green-300" title="Lihat">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </a>
                            <?php else: ?>
                                <span class="text-yellow-400 hover:text-yellow-300 cursor-default flex items-center gap-1"
                                    title="Postingan sedang menunggu persetujuan admin. Tidak dapat dilihat oleh publik.">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span class="text-xs hidden sm:inline">Pending</span>
                                </span>
                            <?php endif; ?>

                            <form id="delete-form-<?php echo e($post->id); ?>" action="<?php echo e(route('posts.destroy', $post->id)); ?>"
                                method="POST" class="inline-block">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="button"
                                    onclick="confirmDelete(event, <?php echo e($post->id); ?>, '<?php echo e($post->title); ?>', 'Postingan')"
                                    class="text-red-400 hover:text-red-300" title="Hapus">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </form>
                        </div>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal352a17f6379e55178f396c3d115b7a53)): ?>
<?php $attributes = $__attributesOriginal352a17f6379e55178f396c3d115b7a53; ?>
<?php unset($__attributesOriginal352a17f6379e55178f396c3d115b7a53); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal352a17f6379e55178f396c3d115b7a53)): ?>
<?php $component = $__componentOriginal352a17f6379e55178f396c3d115b7a53; ?>
<?php unset($__componentOriginal352a17f6379e55178f396c3d115b7a53); ?>
<?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                <?php echo e($posts->withQueryString()->links()); ?>

            </div>
        <?php else: ?>
            <?php if (isset($component)) { $__componentOriginalfe16eb12133e72aabae529d081318460 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfe16eb12133e72aabae529d081318460 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout.empty-state','data' => ['title' => 'Belum ada postingan','message' => ''.e(isset($search) ? 'Tidak ada hasil yang cocok dengan pencarian Anda.' : 'Mulai menulis dan bagikan postingan pertama Anda sekarang.').'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout.empty-state'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Belum ada postingan','message' => ''.e(isset($search) ? 'Tidak ada hasil yang cocok dengan pencarian Anda.' : 'Mulai menulis dan bagikan postingan pertama Anda sekarang.').'']); ?>
                 <?php $__env->slot('icon', null, []); ?> 
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                 <?php $__env->endSlot(); ?>
                 <?php $__env->slot('action', null, []); ?> 
                    <a href="<?php echo e(route('posts.create')); ?>"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Buat Postingan
                    </a>
                 <?php $__env->endSlot(); ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfe16eb12133e72aabae529d081318460)): ?>
<?php $attributes = $__attributesOriginalfe16eb12133e72aabae529d081318460; ?>
<?php unset($__attributesOriginalfe16eb12133e72aabae529d081318460); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfe16eb12133e72aabae529d081318460)): ?>
<?php $component = $__componentOriginalfe16eb12133e72aabae529d081318460; ?>
<?php unset($__componentOriginalfe16eb12133e72aabae529d081318460); ?>
<?php endif; ?>
        <?php endif; ?>
    </div>

    <script>
        function confirmApprove(postId, postTitle) {
            Swal.fire({
                title: 'Setujui Postingan?',
                text: `Apakah Anda yakin ingin menyetujui postingan "${postTitle}"?`,
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#10b981',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Ya, Setujui!',
                cancelButtonText: 'Batal',
                background: '#1f2937',
                color: '#f9fafb'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById(`approve-form-${postId}`).submit();
                }
            });
        }

        function confirmReject(postId, postTitle) {
            Swal.fire({
                title: 'Tolak Postingan?',
                text: `Apakah Anda yakin ingin menolak postingan "${postTitle}"?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Ya, Tolak!',
                cancelButtonText: 'Batal',
                background: '#1f2937',
                color: '#f9fafb'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById(`reject-form-${postId}`).submit();
                }
            });
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.appLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/app/posts/index.blade.php ENDPATH**/ ?>