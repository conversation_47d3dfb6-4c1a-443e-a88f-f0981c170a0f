<div class="p-4 bg-indigo-900/50 rounded-lg mb-6">
    <div class="flex items-center mb-4">
        <div class="w-12 h-12 rounded-full bg-indigo-800/70 flex items-center justify-center text-2xl mr-4">
            {{ $notification->data['icon'] }}
        </div>
        <div>
            <div class="text-indigo-100 font-medium text-lg">{{ $notification->data['message'] }}</div>
            <div class="text-xs text-indigo-300 mt-1">
                {{ is_string($notification->created_at) ? $notification->created_at : $notification->created_at->format('d M Y, H:i:s') }}
                ({{ is_string($notification->created_at) ? $notification->created_at : $notification->created_at->diffForHumans() }})
            </div>
        </div>
    </div>
    <div class="text-indigo-100 mt-4 space-y-4">
        <div class="border-t border-indigo-800/50 pt-4">
            <p class="mb-4">
                {{ $notification->data['description'] ?? 'Ini adalah tampilan detail notifikasi. Anda dapat melihat informasi lebih lanjut di bawah ini.' }}
            </p>

            @if (isset($notification->data['context']))
                <div class="bg-gray-800/70 p-3 rounded-lg mb-4">
                    <h3 class="text-sm font-semibold text-indigo-300 mb-2">Konteks:</h3>
                    <p class="text-sm text-gray-300">{{ $notification->data['context'] }}</p>
                </div>
            @endif

            <div class="bg-gray-800/70 p-3 rounded-lg">
                <h3 class="text-sm font-semibold text-indigo-300 mb-2">Detail Notifikasi:</h3>
                <div class="text-xs font-mono text-gray-300 overflow-auto max-h-40">
                    <pre>{{ json_encode($notification->data, JSON_PRETTY_PRINT) }}</pre>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="mt-6 flex justify-between items-center">
    <a href="{{ route('notifications.index') }}"
        class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-md text-sm flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Kembali
    </a>

    @if (isset($notification->data['href']))
        <a href="{{ $notification->data['href'] }}"
            class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md text-sm flex items-center">
            <span>Buka Tautan</span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
            </svg>
        </a>
    @endif
</div>
