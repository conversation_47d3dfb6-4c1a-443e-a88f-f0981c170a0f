@props(['comments'])

<ul class="comment-list">
    @foreach ($comments as $comment)
        <li>
            <div class="comment-card">
                <blockquote class="card-text">
                    {{ $comment['text'] }}
                </blockquote>
                <div class="profile-card">
                    <img src="{{ $comment['img'] }}"
                         width="48" height="48"
                         loading="lazy"
                         alt="{{ $comment['author'] }}"
                         class="profile-banner"
                         style="border-radius: 50%; object-fit: cover;">
                    <div>
                        <p class="card-title">{{ $comment['author'] }}</p>
                        <time class="card-date" datetime="{{ $comment['datetime'] }}">{{ $comment['date'] }}</time>
                    </div>
                </div>
            </div>
        </li>
    @endforeach
</ul>


