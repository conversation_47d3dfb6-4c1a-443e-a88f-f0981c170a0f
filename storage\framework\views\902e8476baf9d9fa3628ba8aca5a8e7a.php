<div class="relative group">
    <!-- Avatar -->
    <div
        class="relative h-20 w-20 sm:h-24 sm:w-24 rounded-full overflow-hidden border-4 border-indigo-500 shadow-lg cursor-pointer avatar-container">
        <?php if($profile->avatar): ?>
            <img src="<?php echo e(Storage::url($profile->avatar)); ?>" alt="<?php echo e($user->name); ?>" class="h-full w-full object-cover"
                id="avatar-preview" />
        <?php else: ?>
            <div class="h-full w-full flex items-center justify-center bg-gray-700 text-gray-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
            </div>
        <?php endif; ?>

        <!-- Edit overlay -->
        <div
            class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
        </div>
    </div>

    <!-- Status indicator -->
    <div class="absolute bottom-0 right-0 h-5 w-5 bg-green-400 rounded-full border-2 border-white"></div>

    <!-- Hidden file input -->
    <input type="file" name="avatar" id="avatar-upload" class="hidden" accept="image/*" />
</div>

<?php if (! $__env->hasRenderedOnce('5f2ce948-527a-4500-a711-bcafa6736034')): $__env->markAsRenderedOnce('5f2ce948-527a-4500-a711-bcafa6736034'); ?>
    <?php $__env->startPush('scripts'); ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const avatarContainer = document.querySelector('.avatar-container');
                const fileInput = document.getElementById('avatar-upload');
                const avatarPreview = document.getElementById('avatar-preview');

                // Open file dialog when avatar is clicked
                avatarContainer.addEventListener('click', () => {
                    fileInput.click();
                });

                // Handle file selection
                fileInput.addEventListener('change', async (e) => {
                    if (e.target.files.length === 0) return;

                    const file = e.target.files[0];

                    // Show preview immediately
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        if (!avatarPreview) {
                            // Create img element if it doesn't exist
                            const newImg = document.createElement('img');
                            newImg.id = 'avatar-preview';
                            newImg.className = 'h-full w-full object-cover';
                            avatarContainer.innerHTML = '';
                            avatarContainer.appendChild(newImg);
                            avatarPreview = newImg;
                        }

                        avatarPreview.src = e.target.result;
                    };
                    reader.readAsDataURL(file);

                    // Upload to server
                    const formData = new FormData();
                    formData.append('_token', document.querySelector('input[name="_token"]').value);
                    formData.append('_method', 'PUT');
                    formData.append('avatar', file);

                    try {
                        const response = await fetch('<?php echo e(route('profile.update')); ?>', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest',
                                'Accept': 'application/json'
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            // Show success notification
                            Swal.fire({
                                title: 'Berhasil!',
                                text: 'Foto profil berhasil diperbarui',
                                icon: 'success',
                                toast: true,
                                position: 'top-end',
                                showConfirmButton: false,
                                timer: 3000
                            });
                        }
                    } catch (error) {
                        console.error('Error uploading avatar:', error);

                        // Show error notification
                        Swal.fire({
                            title: 'Error!',
                            text: 'Gagal mengunggah foto profil',
                            icon: 'error',
                            toast: true,
                            position: 'top-end',
                            showConfirmButton: false,
                            timer: 3000
                        });
                    }
                });
            });
        </script>
    <?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/components/profile/avatar-enhanced.blade.php ENDPATH**/ ?>