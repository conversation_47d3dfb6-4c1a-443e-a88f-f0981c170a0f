@props(['user' => null])
<header class="header" data-header>
    <div class="container">
        <a href="{{ route('home') }}" class="logo">
            <x-logo.Logo width="200" height="62" />
        </a>

        <nav class="navbar" data-navbar>
            <div class="navbar-top">
                <a href="#" class="logo">
                    <x-logo.Logo width="119" height="37" />
                </a>

                <button class="nav-close-btn" aria-label="close menu" data-nav-toggler>
                    <ion-icon name="close-outline" aria-hidden="true"></ion-icon>
                </button>
            </div>

            <ul class="navbar-list">
                @php
                    use App\Models\Category;
                    $categories = Category::all();
                    $navbarItems = [['href' => route('home'), 'label' => 'Beranda']];
                    foreach ($categories as $category) {
                        $navbarItems[] = [
                            'href' => route('categories.show', $category->slug),
                            'label' => $category->title,
                        ];
                    }
                @endphp

                <x-navbar.NavbarList :items="$navbarItems" />
            </ul>

            <div class="navbar-bottom">
                @auth
                    <div class="profile-card">
                        @if ($user->profile && $user->profile->avatar)
                            <img src="{{ Storage::url($user->profile->avatar) }}"
                                width="48" height="48"
                                alt="{{ $user->name }}"
                                class="profile-banner"
                                style="border-radius: 50%; object-fit: cover; aspect-ratio: 1/1; overflow: hidden;" />
                        @else
                            <img src="https://ui-avatars.com/api/?name={{ urlencode($user->name) }}&background=6366f1&color=fff"
                                width="48" height="48"
                                alt="{{ $user->name }}"
                                class="profile-banner"
                                style="border-radius: 50%; object-fit: cover; aspect-ratio: 1/1; overflow: hidden;" />
                        @endif
                        <div>
                            <p class="card-title">{{ $user->name }}</p>
                            <p class="card-subtitle">{{ $user->role }}</p>
                        </div>
                    </div>
                    <ul class="link-list">
                        @php
                            $navbarBottomLinks = [
                                ['href' => route('app.dashboard'), 'label' => 'Dashboard'],
                                ['href' => '#', 'label' => 'Keluar'],
                            ];
                        @endphp
                        <x-navbar.NavbarLinkList :links="$navbarBottomLinks" />
                    </ul>
                @else
                    <a href="{{ route('login') }}"
                        style="background:#2563eb; color:#fff; padding:8px 20px; border-radius:6px; font-weight:600; text-decoration:none; widhth:100%; text-align:center;">
                        Masuk
                    </a>
                @endauth
            </div>
            <p class="copyright-text">Copyright {{ date('Y') }} © IMM Al-Qossam.</p>
        </nav>

        @auth
            <x-button.PrimaryButton href="{{ route('app.dashboard') }}"
                class="hidden md:inline-block">Dashboard</x-button.PrimaryButton>
        @else
            <x-button.PrimaryButton href="{{ route('login') }}"
                class="hidden md:inline-block">Masuk</x-button.PrimaryButton>
        @endauth

        <button class="nav-open-btn" aria-label="open menu" data-nav-toggler>
            <ion-icon name="menu-outline" aria-hidden="true"></ion-icon>
        </button>
    </div>
</header>

