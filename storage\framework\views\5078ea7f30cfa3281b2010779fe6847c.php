<?php $__env->startSection('title', 'Struktur Organisasi - PK IMM Al-Qossam'); ?>

<?php $__env->startSection('content'); ?>
    <style>
        .org-structure {
            padding-top: 90px;
        }

        .structure-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .structure-card {
            background-color: var(--bg-primary);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: 0.3s ease;
            text-align: center;
            padding: 30px 20px;
        }

        .structure-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .structure-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            margin: 0 auto 20px;
            overflow: hidden;
            border: 4px solid var(--accent);
            position: relative;
        }

        .structure-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .structure-avatar-placeholder {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--accent), var(--accent-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            font-weight: bold;
        }

        .structure-name {
            color: var(--foreground-primary);
            font-weight: 600;
            font-size: 1.3rem;
            margin-bottom: 8px;
        }

        .structure-position {
            color: var(--accent);
            font-weight: 500;
            font-size: 1rem;
            margin-bottom: 15px;
        }

        .structure-description {
            color: var(--foreground-secondary);
            line-height: 1.6;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .structure-order {
            position: absolute;
            top: 15px;
            right: 15px;
            background-color: var(--accent);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.8rem;
        }

        .view-detail-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            background-color: var(--accent);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: 0.3s ease;
        }

        .view-detail-btn:hover {
            background-color: var(--accent-dark);
            transform: translateY(-2px);
        }

        .no-structures {
            text-align: center;
            padding: 60px 20px;
            color: var(--foreground-secondary);
        }

        .no-structures ion-icon {
            font-size: 4rem;
            color: var(--accent);
            margin-bottom: 20px;
        }

        .back-home-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background-color: var(--bg-secondary);
            color: var(--foreground-primary);
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: 0.3s ease;
            margin-bottom: 30px;
        }

        .back-home-btn:hover {
            background-color: var(--accent);
            color: white;
        }

        @media (max-width: 768px) {
            .structure-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .structure-avatar {
                width: 120px;
                height: 120px;
            }

            .structure-avatar-placeholder {
                font-size: 2.5rem;
            }
        }
    </style>

    <section class="section org-structure" aria-labelledby="org-structure-label">
        <div class="container">
            <a href="<?php echo e(route('home')); ?>" class="back-home-btn">
                <ion-icon name="arrow-back-outline" aria-hidden="true"></ion-icon>
                <span>Kembali ke Beranda</span>
            </a>

            <div class="text-center mb-8">
                <h1 class="headline headline-1 section-title" id="org-structure-label">
                    <span class="span">Struktur Organisasi</span>
                </h1>
                <p class="section-text">
                    Kenali pengurus PK IMM Al-Qossam periode 2023-2024 yang berkomitmen memajukan dakwah Islam di kampus
                </p>
            </div>

            <?php if(count($structures) > 0): ?>
                <div class="structure-grid">
                    <?php $__currentLoopData = $structures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $structure): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <article class="structure-card">
                            <?php if($structure->order): ?>
                                <div class="structure-order"><?php echo e($structure->order); ?></div>
                            <?php endif; ?>

                            <figure class="structure-avatar">
                                <?php if($structure->image): ?>
                                    <img src="<?php echo e(asset('storage/' . $structure->image)); ?>" alt="<?php echo e($structure->name); ?>"
                                        loading="lazy" />
                                <?php else: ?>
                                    <div class="structure-avatar-placeholder">
                                        <?php echo e(strtoupper(substr($structure->name, 0, 1))); ?>

                                    </div>
                                <?php endif; ?>
                            </figure>

                            <h2 class="structure-name"><?php echo e($structure->name); ?></h2>
                            <p class="structure-position"><?php echo e($structure->position_name); ?></p>

                            <?php if($structure->description): ?>
                                <p class="structure-description">
                                    <?php echo e(Str::limit($structure->description, 100)); ?>

                                </p>
                            <?php endif; ?>

                            <a href="<?php echo e(route('organization.show', $structure->id)); ?>" class="view-detail-btn">
                                <span>Lihat Detail</span>
                                <ion-icon name="arrow-forward-outline" aria-hidden="true"></ion-icon>
                            </a>
                        </article>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="no-structures">
                    <ion-icon name="people-outline" aria-hidden="true"></ion-icon>
                    <h2 class="headline headline-2">Belum Ada Struktur Organisasi</h2>
                    <p>Struktur organisasi sedang dalam proses penyusunan. Silakan kembali lagi nanti.</p>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <section class="section cta" aria-labelledby="cta-label">
        <div class="container">
            <div class="card cta-card">
                <div class="card-content">
                    <h2 class="headline headline-2 card-title" id="cta-label">
                        Bergabung dengan PK IMM Al-Qossam
                    </h2>
                    <p class="card-text">
                        Jadilah bagian dari gerakan mahasiswa Islam yang memperjuangkan nilai-nilai kepemimpinan,
                        keilmuan, dan dakwah di kampus. Mari bersama membangun masa depan yang lebih baik.
                    </p>
                    <a href="<?php echo e(route('register')); ?>" class="btn btn-primary">
                        <span class="span">Daftar Sekarang</span>
                        <ion-icon name="arrow-forward-outline" aria-hidden="true"></ion-icon>
                    </a>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.homeLayout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Belajar Laravel\imm-alqossam\resources\views/organization/index.blade.php ENDPATH**/ ?>